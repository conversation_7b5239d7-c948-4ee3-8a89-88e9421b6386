#!/bin/bash

# 生成完整的compile_commands.json的脚本
# 这个脚本会扫描整个gw_parser目录并生成编译命令

PROJECT_ROOT="/Users/<USER>/gw-hw"
OUTPUT_FILE="$PROJECT_ROOT/compile_commands.json"

# 基础编译标志
BASE_CFLAGS="-D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -g -fvisibility=hidden -fPIC -std=c++11 -Wall"
INCLUDE_FLAGS="-I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include"

echo "生成compile_commands.json..."
echo "[" > "$OUTPUT_FILE"

FIRST_ENTRY=true

# 查找所有C++源文件
find "$PROJECT_ROOT/src/hw/gw_parser" -name "*.cpp" -o -name "*.c" | while read -r file; do
    # 获取文件所在目录
    DIR=$(dirname "$file")
    FILENAME=$(basename "$file")
    
    # 判断是否为C++文件
    if [[ "$file" == *.cpp ]]; then
        COMPILER="g++"
        FLAGS="$BASE_CFLAGS"
    else
        COMPILER="gcc"
        FLAGS="-g -fvisibility=hidden -fPIC"
    fi
    
    # 添加逗号（除了第一个条目）
    if [ "$FIRST_ENTRY" = false ]; then
        echo "," >> "$OUTPUT_FILE"
    fi
    FIRST_ENTRY=false
    
    # 生成编译命令条目
    cat >> "$OUTPUT_FILE" << EOF
  {
    "directory": "$DIR",
    "command": "$COMPILER $FLAGS $INCLUDE_FLAGS -c $FILENAME",
    "file": "$file"
  }EOF
done

echo "" >> "$OUTPUT_FILE"
echo "]" >> "$OUTPUT_FILE"

echo "compile_commands.json 生成完成！"
echo "文件位置: $OUTPUT_FILE"