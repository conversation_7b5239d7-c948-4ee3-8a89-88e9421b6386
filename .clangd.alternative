CompileFlags:
  Add:
    # C++标准
    - -std=c++11
    
    # 编译器定义
    - -D_CC_GNU_PP
    - -DNDEBUG
    - -D_ENABLE_FLUSH_FILE=1
    
    # 基础包含路径
    - -I.
    - -I./src/hw/gw_parser/include
    - -I./src/hw/gw_parser/utils/queue
    - -I./src/hw/gw_parser/utils/cjson
    - -I./src/hw/gw_parser/core/l4
    - -I./src/hw/gw_parser/core
    - -I./src/hw/gw_parser/utils
    
    # 解析器相关路径
    - -I./src/hw/gw_parser/parser/oracle_parser
    - -I./src/hw/gw_parser/parser/mysql_parser
    - -I./src/hw/gw_parser/parser/postgre_parser
    - -I./src/hw/gw_parser/parser/ssl_parser
    - -I./src/hw/gw_parser/parser/http_parser
    - -I./src/hw/gw_parser/parser/http2_parser
    - -I./src/hw/gw_parser/parser/grpc_parser
    - -I./src/hw/gw_parser/parser/mongo_parser
    - -I./src/hw/gw_parser/parser/ftp_parser
    - -I./src/hw/gw_parser/parser
    
    # 数据源和上传模块路径
    - -I./src/hw/gw_parser/source
    - -I./src/hw/gw_parser/source/dpdk_source
    - -I./src/hw/gw_parser/source/nic_source
    - -I./src/hw/gw_parser/source/pcap_source
    - -I./src/hw/gw_parser/source/file_source
    - -I./src/hw/gw_parser/upload
    - -I./src/hw/gw_parser/upload/kafka_upload
    - -I./src/hw/gw_parser/upload/log_upload
    - -I./src/hw/gw_parser/upload/diy_upload
    - -I./src/hw/gw_parser/upload/web_upload
    
    # 第三方库包含路径
    - -I./src/hw/gw_parser/nacos_c++/include
    - -I./src/hw/gw_parser/libaws_api_c++/include
    - -I./src/hw/gw_parser/libmagic/include
    - -I./src/hw/gw_parser/liblicutils_c_sdk
    
    # 系统包含路径
    - -I/usr/include
    - -I/usr/local/include
    - -I/usr/libiconv/include
    
    # 编译器标志
    - -fPIC
    - -Wall
    - -g
    
    # 针对macOS的特殊配置
    - -D_DARWIN_C_SOURCE
    
  Remove:
    # 移除可能导致问题的标志
    - -fsanitize=address
    - -fno-omit-frame-pointer

# 诊断配置 - 精细控制版本
Diagnostics:
  ClangTidy:
    Add: 
      - readability-*
      - performance-*
      # 只启用有用的modernize规则
      - modernize-avoid-bind
      - modernize-deprecated-headers
      - modernize-loop-convert
      - modernize-make-shared
      - modernize-make-unique
      - modernize-pass-by-value
      - modernize-redundant-void-arg
      - modernize-replace-auto-ptr
      - modernize-shrink-to-fit
    Remove:
      # 屏蔽影响体验的modernize规则
      - modernize-use-using              # 不建议typedef改using
      - modernize-use-trailing-return-type
      - modernize-use-auto               # 不强制使用auto
      - modernize-use-bool-literals      # 不强制bool字面量
      - modernize-use-default-member-init
      - modernize-use-equals-default
      - modernize-use-equals-delete
      - modernize-use-nullptr            # 不强制使用nullptr
      - modernize-use-override           # 不强制override关键字
      - modernize-use-transparent-functors
      # 屏蔽特定的可读性规则
      - readability-magic-numbers
      - readability-identifier-length
      - readability-function-cognitive-complexity
      - readability-else-after-return
      - readability-implicit-bool-conversion
  UnusedIncludes: None
  MissingIncludes: None

# 索引配置
Index:
  Background: Build
  StandardLibrary: Yes

# 代码补全配置
Completion:
  AllScopes: Yes