# CLangd 配置指南

本项目已配置了CLangd语言服务器，用于改善代码索引和智能提示功能。

## 配置文件说明

### 1. `.clangd` 文件
项目根目录下的 `.clangd` 文件提供了全局的编译标志配置：

- **编译标准**: C++11
- **预处理器定义**: 包含项目特定的宏定义
- **包含路径**: 覆盖所有主要的头文件目录
- **诊断配置**: 启用有用的代码分析规则
- **代码补全**: 优化了补全体验

### 2. `compile_commands.json` 文件
为Oracle解析器模块生成了精确的编译命令数据库：

- 包含28个编译单元的完整编译命令
- 基于Makefile配置生成，确保与实际构建一致
- 支持CLangd精确的符号解析和跳转

## 使用方法

### 重新生成 compile_commands.json
如果添加了新的源文件或修改了Makefile，可以重新生成编译命令：

```bash
# 使用Python脚本（推荐）
python3 generate_oracle_compile_commands.py

# 或使用shell脚本（如果需要更全面的覆盖）
./generate_compile_commands.sh
```

### 重启语言服务器
配置文件更新后，在IDE中重启CLangd服务器：

1. **VSCode**: 使用命令面板 "clangd: Restart language server"
2. **其他IDE**: 查看相应的语言服务器重启选项

## 解决的问题

1. **未定义符号警告**: 通过正确的包含路径配置解决
2. **缺失头文件**: 添加了所有必要的系统和项目头文件路径
3. **宏定义不识别**: 包含了项目特定的预处理器定义
4. **代码补全不准确**: 提供了精确的编译上下文

## 目录结构说明

配置覆盖了以下主要目录：

```
src/hw/gw_parser/
├── include/           # 公共头文件
├── core/             # 核心功能
├── utils/            # 工具函数
├── parser/           # 各种协议解析器
│   ├── oracle_parser/
│   ├── mysql_parser/
│   ├── postgre_parser/
│   └── ...
├── source/           # 数据源模块
├── upload/           # 数据上传模块
└── nacos_c++/        # 第三方库
```

## 故障排除

### 如果CLangd仍然报告未解析的符号：

1. 检查文件路径是否正确
2. 确认所有必要的头文件都存在
3. 重新生成 `compile_commands.json`
4. 重启CLangd服务器
5. 检查IDE的CLangd插件配置

### 控制Clang-Tidy规则：

如果你觉得clang-tidy的建议过于频繁或影响使用体验：

**完全禁用modernize规则**（默认配置）：
- 屏蔽所有 `modernize-*` 规则
- 不会显示 typedef 改 using 的建议
- 不会显示其他现代化建议

**精细控制**（使用备选配置）：
```bash
# 使用精细控制的配置
cp .clangd.alternative .clangd
```

**完全禁用clang-tidy**：
在 `.clangd` 中添加：
```yaml
Diagnostics:
  Suppress: "*"
```

### 性能问题：
如果CLangd消耗过多资源，可以调整 `.clangd` 文件中的索引配置：

```yaml
Index:
  Background: Skip  # 跳过后台索引
```

## 注意事项

- `.clangd` 文件使用YAML格式，注意缩进
- `compile_commands.json` 使用JSON格式
- 路径分隔符在不同系统上可能不同
- 第三方库路径可能需要根据实际安装位置调整