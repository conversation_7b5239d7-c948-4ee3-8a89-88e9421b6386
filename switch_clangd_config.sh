#!/bin/bash

# CLangd 配置切换脚本
# 用于在不同的clang-tidy规则配置之间快速切换

PROJECT_ROOT="/Users/<USER>/gw-hw"
cd "$PROJECT_ROOT"

echo "CLangd 配置切换脚本"
echo "===================="
echo "1. 默认配置 (完全屏蔽modernize规则)"
echo "2. 精细控制配置 (只屏蔽影响体验的规则)"
echo "3. 最小诊断配置 (几乎不显示任何建议)"
echo "4. 查看当前配置状态"
echo "5. 退出"
echo ""

read -p "请选择配置选项 (1-5): " choice

case $choice in
    1)
        echo "切换到默认配置..."
        # 当前的.clangd就是默认配置
        echo "✓ 已使用默认配置 (完全屏蔽modernize规则)"
        ;;
    2)
        echo "切换到精细控制配置..."
        if [ -f ".clangd.alternative" ]; then
            cp .clangd .clangd.backup
            cp .clangd.alternative .clangd
            echo "✓ 已切换到精细控制配置"
        else
            echo "✗ 精细控制配置文件不存在"
            exit 1
        fi
        ;;
    3)
        echo "创建最小诊断配置..."
        cp .clangd .clangd.backup
        
        # 创建最小诊断配置
        cat > .clangd << 'EOF'
CompileFlags:
  Add:
    - -std=c++11
    - -D_CC_GNU_PP
    - -DNDEBUG
    - -D_ENABLE_FLUSH_FILE=1
    - -I.
    - -I./src/hw/gw_parser/include
    - -I./src/hw/gw_parser/utils/queue
    - -I./src/hw/gw_parser/utils/cjson
    - -I./src/hw/gw_parser/core/l4
    - -I./src/hw/gw_parser/core
    - -I./src/hw/gw_parser/utils
    - -I./src/hw/gw_parser/parser
    - -I/usr/include
    - -I/usr/local/include
    - -fPIC
    - -Wall
    - -g
  Remove:
    - -fsanitize=address
    - -fno-omit-frame-pointer

Diagnostics:
  Suppress: "*"
  
Index:
  Background: Build
  StandardLibrary: Yes

Completion:
  AllScopes: Yes
EOF
        echo "✓ 已切换到最小诊断配置 (屏蔽所有建议)"
        ;;
    4)
        echo "当前配置状态："
        echo "==============="
        if grep -q "Suppress.*\*" .clangd 2>/dev/null; then
            echo "📋 最小诊断配置 (屏蔽所有建议)"
        elif grep -q "modernize-use-using" .clangd 2>/dev/null; then
            echo "📋 精细控制配置"
        elif grep -q "modernize-\*" .clangd 2>/dev/null; then
            echo "📋 默认配置 (屏蔽modernize规则)"
        else
            echo "📋 未知配置"
        fi
        echo ""
        echo "配置文件内容预览："
        echo "Diagnostics 部分："
        grep -A 10 "Diagnostics:" .clangd 2>/dev/null || echo "未找到Diagnostics配置"
        ;;
    5)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选项"
        exit 1
        ;;
esac

echo ""
echo "🔄 请重启IDE中的CLangd服务器以应用新配置"
echo "💡 如需恢复原配置，备份文件为: .clangd.backup"