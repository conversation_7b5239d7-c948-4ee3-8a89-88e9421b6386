#!/bin/bash

# 为 gw_stats_srv_oatpp 项目生成 compile_commands.json 条目的脚本

PROJECT_ROOT="/Users/<USER>/gw-hw"
OATPP_ROOT="$PROJECT_ROOT/src/hw/gw_stats_srv_oatpp"

# 基础编译标志（基于 CMakeLists.txt）
BASE_CFLAGS="-g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall"

# 包含路径（基于 CMakeLists.txt 中的 include_directories）
INCLUDE_FLAGS="-I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include"

echo "为 gw_stats_srv_oatpp 生成编译命令条目..."

# 生成条目的函数
generate_entry() {
    local file_path="$1"
    local relative_path="${file_path#$OATPP_ROOT/}"
    local filename=$(basename "$file_path")
    
    cat << EOF
  {
    "directory": "$OATPP_ROOT",
    "command": "g++ $BASE_CFLAGS $INCLUDE_FLAGS -c $relative_path",
    "file": "$file_path"
  }
EOF
}

# 查找所有 C++ 源文件
find "$OATPP_ROOT/src" -name "*.cpp" | while read -r file; do
    generate_entry "$file"
    echo ","
done

echo "编译命令条目生成完成！"