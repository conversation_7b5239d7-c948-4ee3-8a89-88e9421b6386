#ifndef __PARSER_ORACLE_PARSER_TNS_DATATYPE_REPRESENTATION_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_DATATYPE_REPRESENTATION_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class DataTypeRepresentationRequest{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			inCharset = stream.getHostShort();
			outCharset = stream.getHostShort();
			clientFlag = stream.unmarshalUB1();

			if (stream.hasData())
			{
				int size = stream.unmarshalUB1();
				stream.getByteBuffer(compileTimeCap,size);
				if (compileTimeCap.length()>7)
				{
					uint8_t ttcVersion = compileTimeCap[7];
					if (ttcVersion<stream.getTTCVersion())
					{
						stream.property.ttcVersions = {ttcVersion};
						stream.SetProperty();
					}
				}

				size = stream.unmarshalUB1();
				stream.getByteBuffer(runtimeCap,size);

				if (runtimeCap.length()>1 && (runtimeCap[1] &0x1) == 0x1)
				{
					stream.getByteBuffer(localTimeZone,11);

					if (compileTimeCap.length()>37 && (compileTimeCap[37]&0x2) == 2)
					{
						std::string tmp;
						stream.getByteBuffer(tmp,4);
					}
				}
			}

			stream.clearData();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}


	int inCharset = 0;
	int outCharset = 0;
	int clientFlag = 0;
	std::string compileTimeCap = "";
	std::string runtimeCap = "";
	std::string localTimeZone = "";
};
}
}


#endif //__PARSER_ORACLE_PARSER_TNS_DATATYPE_REPRESENTATION_MESSAGE_H__