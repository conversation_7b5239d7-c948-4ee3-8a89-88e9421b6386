#ifndef __PARSER_ORACLE_PARSER_TNS_DCB_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_DCB_MESSAGE_H__

#include <set>
#include <tuple>

#include "session_data.h"
#include "tns_uds_message.h"

namespace parser{
namespace oracle{

class DcbMessage{
public:
	bool parseFromStream(SessionData &stream){		
		try{
			parseDcb(stream);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}


	void parseDcb(SessionData &stream){
		int size = 0;
		columnnum = 0;
		headers.clear();
		if ((stream.getProtoVersion() >= 314) && (stream.platform!=ClientPlatform::Jdbc))
		{
			size = stream.unmarshalUB4();
		}else{
			size = stream.unmarshalUB1();
		}
		std::string tmpbuf = "";
		stream.getByteBuffer(tmpbuf,size);
		stream.unmarshalUB4();

		columnnum = stream.unmarshalUB4();
		if (columnnum > 0)
		{
			stream.unmarshalUB1();
		}
		for (int i = 0; i < columnnum; ++i)
		{
			UdsMessage uds;
			uds.parseFromStream(stream);
			ResultSet rs;
			rs.type = uds.oac.dty;
			rs.column_name = uds.udscolmn;
			headers.push_back(rs);
		}

		stream.setResultSet(headers);
		std::string tmp = "";
		stream.unmarshalDALC(tmp);
		if (stream.getTTCVersion()>=3)
		{
			stream.unmarshalUB4();
			stream.unmarshalUB4();
			if (stream.getTTCVersion()>=4)
			{
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				if (stream.getTTCVersion()>=5)
				{
					stream.unmarshalDALC(tmp);
				}
			}
		}
	}

	bool checkResult(SessionData &stream){
		if (stream.hasData() && ((stream.getChar(0)& 0x08)!=0x08))
		{
			return false;
		}

		if (headers.size()<1)
		{
			return false;
		}

		for(ResultSet& rs :headers){
			for(uint8_t c :rs.column_name){
				if (c<0x20 || c>0x7e)
				{
					return false;
				}
			}
		}

		return true;
	}

	static void CheckMessage(SessionData &stream){
		if (stream.property.ttcVersions.size()<2)
		{
			return;
		}
		int32_t curOffset = stream.getOffset();
		std::set<int> pfset;
		std::set<int> tvset;
		std::set<int> pvset;
		std::set<int> cnset;
		std::set<int> cpset;
		std::vector<std::tuple<int,int,int,int,int>> matchs;
		for(int pf : stream.property.platforms){
			for(int tv : stream.property.ttcVersions){
				for(int pv : stream.property.protoVersions){
					for(int cn :stream.property.codeNumber){
						for(int cp :stream.property.codePtr){
							DcbMessage dcb;
							try{
								stream.setOffset(curOffset);
								stream.SetPropertyValue(pf,tv,pv,cn,cp);
								dcb.parseDcb(stream);
							}catch(OverFlow&){
								continue;
							}

							if (!dcb.checkResult(stream))
							{
								continue;
							}

							pfset.insert(pf);
							tvset.insert(tv);
							pvset.insert(pv);
							cnset.insert(cn);
							cpset.insert(cp);
							matchs.push_back(std::make_tuple(pf,tv,pv,cn,cp));
						}
					}
				}
			}
		}
		stream.setOffset(curOffset);
		if (matchs.size()>0)
		{
			std::tuple<int,int,int,int,int> def = matchs[0];
			stream.SetPropertyValue(std::get<0>(def),std::get<1>(def),std::get<2>(def),std::get<3>(def),std::get<4>(def));
			stream.property.platforms.assign(pfset.begin(),pfset.end());
			stream.property.ttcVersions.assign(tvset.begin(),tvset.end());
			stream.property.protoVersions.assign(pvset.begin(),pvset.end());
			stream.property.codeNumber.assign(cnset.begin(),cnset.end());
			stream.property.codePtr.assign(cpset.begin(),cpset.end());
		}else{
			stream.SetProperty();
		}
	}


	int columnnum = 0;
	std::vector<ResultSet> headers;
};

}
}


#endif //__PARSER_ORACLE_PARSER_TNS_DCB_MESSAGE_H__