#ifndef __PARSER_ORACLE_PARSER_TNS_UDS_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_UDS_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class UdsMessage{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		// try{
			oac.parseFromStream(stream);
			if (stream.platform == ClientPlatform::Linux)
			{
				stream.unmarshalUB1();
				stream.unmarshalUB2();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
			}

			int u1 = stream.unmarshalUB1();
			udsnull = (u1>0);
			udscnl = stream.unmarshalUB1();
			stream.unmarshalDALC(udscolmn);
			stream.unmarshalDALC(udssnm);
			snnumcar = udssnm.length();

			stream.unmarshalDALC(udstnm);
			tnnumchar = udstnm.length();
			udstnl = udstnm.length();
			
			
			if (stream.getTTCVersion()>=3)
			{
				udskpos = stream.unmarshalUB2();
				if (stream.getTTCVersion()>=6)
				{
					udsflg = stream.unmarshalUB4();
				}
			}
		// }catch(OverFlow&){
		// 	stream.clearData();
		// 	return false;
		// }
		return true;
	}


	OacMessage oac;
	bool udsnull = false;
	int udscnl = 0;
	std::string udscolmn = "";
	std::string udssnm = "";
	int snnumcar = 0;
	std::string udstnm = "";
	int tnnumchar = 0;
	int udstnl = 0;
	int udskpos = 0;
	int udsflg = 0;

};
}
}
#endif //__PARSER_ORACLE_PARSER_TNS_UDS_MESSAGE_H__