#ifndef __PARSER_ORACLE_PARSER_TNS_OSES_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_OSES_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class OsesMessage{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
		stream.unmarshalUB1();
		idx = stream.unmarshalUB4();
		ser = stream.unmarshalUB4();
		opc = stream.unmarshalUB4();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	int idx = 0;
	int ser = 0;
	int opc = 0;
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_OSES_MESSAGE_H__