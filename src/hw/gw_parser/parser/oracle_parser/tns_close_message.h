#ifndef __PARSER_ORACLE_PARSER_TNS_CLOSE_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_CLOSE_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class ClsoeMessage{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
		stream.unmarshalUB1();

		unsigned char tmp[] = {0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xfe};
		unsigned char tmp2[] = {0xfe,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
		if (stream.check(8,tmp,8))  //不压缩
		{						
			stream.property.codePtr = {0};
		}else if (stream.check(8,tmp2,8)){		//不压缩		
			stream.property.codePtr = {2};
		}else{
			stream.property.codePtr = {0};
		}
		stream.setTypeRep(4,stream.property.codePtr[0]);
		stream.unmarshalPtr();


		if(checkMessage(stream)){			
			int offset = stream.unmarshalUB4();
			for (int i = 0; i < offset; ++i)
			{
				stream.unmarshalUB4();
			}

			if (stream.platform == ClientPlatform::Linux)
			{
				stream.unmarshalUB4();
			}
		}
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}


	bool checkMessage(SessionData &stream){ 
		bool needagain = true;
		if (stream.property.codeNumber.size()>1)
		{
			unsigned char tmp3[] = {0x03,0x5e};
			unsigned char tmp4[] = {0x03,0x43};
			unsigned char tmp5[] = {0x03,0x05};
			unsigned char tmp6[] = {0x03,0x4e};
			uint32_t curOffset = stream.getOffset();
			std::set<int> cnset;
			std::set<int> pfset;
			for(int cn :stream.property.codeNumber){
				for(int pf: stream.property.platforms){
					try{
						needagain = false;
						stream.setOffset(curOffset);
						stream.platform = (ClientPlatform)pf;
						stream.setTypeRep(1,cn);
						stream.setTypeRep(2,cn);
						stream.setTypeRep(3,cn);

						int offset = stream.unmarshalUB4();
						for (int i = 0; i < offset; ++i)
						{
							stream.unmarshalUB4();
						}

						if (stream.platform == ClientPlatform::Linux)
						{
							stream.unmarshalUB4();
						}

						if(stream.check(0,tmp3,2) || stream.check(0,tmp4,2) || stream.check(0,tmp5,2) || stream.check(0,tmp6,2)){
							cnset.insert(cn);
							pfset.insert(pf);
						}

					}catch(OverFlow&){
						needagain = true;
					}
				}
			}

			if (cnset.size() <1 || pfset.size()<1)
			{
				return true;
			}

			stream.property.codeNumber.assign(cnset.begin(),cnset.end());
			stream.property.platforms.assign(pfset.begin(),pfset.end());
			stream.SetProperty();

			if (needagain)
			{				
				stream.setOffset(curOffset);
			}
		}
		return needagain;
	}

};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_CLOSE_MESSAGE_H__