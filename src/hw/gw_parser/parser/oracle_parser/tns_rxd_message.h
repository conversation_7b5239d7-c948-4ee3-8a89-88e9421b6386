#ifndef __PARSER_ORACLE_PARSER_TNS_RXD_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_RXD_MESSAGE_H__

#include <string>
#include <sstream>

#include "session_data.h"

namespace parser{
namespace oracle{

class RxdMessage{
public:
	RxdMessage(BindInfo info):bindInfo(info){};

	BindInfo bindInfo;
	int code = 0;
	std::vector<std::string> values;
	std::vector<int> postPoned;

	static std::string kgrd42b(int value,int size){
		std::string result = "";
		uint8_t kgrd_basis_64[] = {65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 43, 47};
		for (int i = 0; i < size; ++i)
		{
			result += (char)(kgrd_basis_64[value & 0x3f]);
			value = ((value >>6) & 0x3FFFFFF);
		}
		return result;
	}

	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		// try{
			code = stream.unmarshalUB1();

			for (int i = 0; i < bindInfo.bindtypes.size(); ++i)
			{
				int datatype = bindInfo.bindtypes[i];
				switch(datatype){
				case 116:{
					stream.unmarshalUB1();
					stream.unmarshalUB1();
					break;				
				}
				case 109:{
					std::string tmp;
					stream.unmarshalDALC(tmp);
					stream.unmarshalDALC(tmp);
					stream.unmarshalDALC(tmp);
					stream.unmarshalUB2();
					int len = stream.unmarshalUB4();
					stream.unmarshalUB2();
					if (len > 0)
					{
						std::string value = "";
						stream.unmarshalCLR(value,len);
						values.push_back(value);
					}
					break;
				}
				case 8:
				case 24:{
					values.push_back("");
					postPoned.push_back(i);	
					break;			
				}
				case 112:
				case 113:
				case 114:{
					int len = stream.unmarshalUB4();
					std::string value = "";
					stream.unmarshalCLR(value,len);
					values.push_back(value);
					break;
				}
				case 998:{
					int len = stream.unmarshalUB4();
					for (int i = 0; i < len; ++i)
					{
						std::string value = "";
						stream.unmarshalCLRNoLimit(value);
						values.push_back(value);
					}
					break;			
				}
				case 104:{
					int size =stream.unmarshalUB1();
					if (size == 14)
					{
						int l1 = stream.unmarshalUB4();
						int l2 = stream.unmarshalUB2();
						stream.unmarshalUB1();
						int l3 = stream.unmarshalUB4();
						int l4 = stream.unmarshalUB2();
						if (l1!=0 && l2!=0 && l3!=0 && l4!=0)
						{
							std::string rowid = kgrd42b(l1,6);
							rowid += kgrd42b(l2,3);
							rowid += kgrd42b(l3,6);
							rowid += kgrd42b(l4,3);
							values.push_back(rowid);
						}
					}
					break;
				}
				case 208:{
					int size = stream.unmarshalUB4();
					std::string value = "";
					stream.unmarshalCLR(value,size);
					values.push_back(value);
					break;
				}
				case 996:{
					std::string value = "";
					stream.unmarshalCLRNoLimit(value);
					if (value.length()>0 && (value.length()%2 == 0) )
					{
						for (int i = 0; i < value.length();)
						{
							char tmp = value[i+1];
							value[i+1] = value[i];
							value[i] = tmp;
							i += 2;
						}
						value = stream.coverUTF16toUTF8(value);//boost::locale::conv::between(value.data(),"UTF-8","UTF-16");
						values.push_back(value);
					}

					break;
				}
				default:{
					std::string value = "";
					stream.unmarshalCLRNoLimit(value);
					values.push_back(value);
					break;
				}
				}
			}

			for (int i = 0; i < values.size(); ++i)
			{
				if (values[i].size() == 0)
				{
					continue;
				}

				int datatype = bindInfo.bindtypes[i];
				switch(datatype){
				case 2:
					values[i] = stream.transToNumber(values[i]);
					break;
				case 12:
					values[i] = stream.transToDataTime(values[i]);
					break;
				case 100:
					values[i] = stream.transToBinaryFloat(values[i]);
					break;
				case 101:
					values[i] = stream.transToBinaryDouble(values[i]);
					break;
				default:
					continue;
				}
			}
		// }catch(OverFlow&){
		// 	stream.clearData();
		// 	return false;
		// }

		return true;
	}
};

}
}

#endif //__PARSER_ORACLE_PARSER_TNS_RXD_MESSAGE_H__