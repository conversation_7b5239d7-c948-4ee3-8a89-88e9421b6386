#ifndef __PARSER_ORACLE_PARSER_TNS_ALL_READ_RXD_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_ALL_READ_RXD_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class AllReadRxdMessage{
public:
	bool parseFromStream(SessionData &stream){
		std::vector<ResultSet> header = stream.getResultSet();
		// int offset = stream.getOffset();
		try{

		if (stream.outBindValue.size()>0)
		{
			//数据不足，暂时不能正确解析
			stream.outBindValue.clear();
			stream.clearData();
			return false;
		}
			for (int i = 0; i < header.size(); ++i)
			{
				if (stream.bvcValue.length()>0 && stream.bvcValue.length()>=i)
				{
					if (stream.bvcValue[i] == '0')
					{
						values.push_back("");
						continue;
					}
				}
				int datatype = header[i].type;
				switch(datatype){
				case 116:{
					stream.unmarshalUB1();
					stream.unmarshalUB1();
					break;
				}				
				case 109:{
					std::string tmp;
					stream.unmarshalDALC(tmp);
					stream.unmarshalDALC(tmp);
					stream.unmarshalDALC(tmp);
					stream.unmarshalUB2();
					int size = stream.unmarshalUB4();
					stream.unmarshalUB2();
					if (size > 0)
					{
						std::string value = "";
						stream.unmarshalCLR(value,size);
						values.push_back(value);
					}
					break;
				}				
				case 8:
				case 24:{
					values.push_back("");
					postPoned.push_back(i);
					break;
				}
				case 112:
				case 113:
				case 114:{
					int size = stream.unmarshalUB4();
					std::string value="";
					stream.unmarshalCLR(value,size);
					values.push_back(value);
					break;
				}
				case 998:{
					int size = stream.unmarshalUB4();
					for (int i = 0; i < size; ++i)
					{
						std::string value = "";
						stream.unmarshalCLRNoLimit(value);
						values.push_back(value);
					}
					break;
				}
				case 104:{
					int size = stream.unmarshalUB1();
					if (size == 14)
					{
						int l1 = stream.unmarshalUB4();
						int l2 = stream.unmarshalUB2();
						stream.unmarshalUB1();
						int l3 = stream.unmarshalUB4();
						int l4 = stream.unmarshalUB2();
						if (l1 != 0 && l2!=0 && l3!=0 && l4 !=0)
						{
							std::string rowid = "";
							rowid += RxdMessage::kgrd42b(l1,6);
							rowid += RxdMessage::kgrd42b(l2,3);
							rowid += RxdMessage::kgrd42b(l3,6);
							rowid += RxdMessage::kgrd42b(l4,3);
							values.push_back(rowid);
						}
					}
					break;
				}
				case 208:{
					int size = stream.unmarshalUB4();
					std::string value = "";
					stream.unmarshalCLR(value,size);
					values.push_back(value);
					break;
				}
				case 996:{
					std::string charbuf = "";
					stream.unmarshalCLRNoLimit(charbuf);
					if (charbuf.length()>0 && charbuf.length()%2!=0)
					{
						for (int i = 0; i < charbuf.length(); )
						{
							char tmp = charbuf[i+1];
							charbuf[i+1] = charbuf[i];
							charbuf[i]=tmp;
							i+=2;
						}
						charbuf = stream.coverUTF16toUTF8(charbuf);//boost::locale::conv::between(value.data(),"UTF-8","UTF-16");
						values.push_back(charbuf);
					}
					break;
				}
				default:{
					std::string value = "";				
					stream.unmarshalCLRNoLimit(value);
					values.push_back(value);
				}

				}
			}



			for (int i = 0; i < postPoned.size(); ++i)
			{
				int datatype = header[postPoned[i]].type;
				switch(datatype){
				case 8:
				case 24:{
					std::string value = "";
					while(true){
						int size = stream.unmarshalUB1();
						if (size == 0)
						{
							break;
						}
						std::string tmp = "";
						stream.getByteBuffer(tmp,size);
						value += tmp;
						values[postPoned[i]] = value;
					}
				}				
					break;
				}
			}

			for (int i = 0; i < values.size(); ++i)
			{			
				if (values[i].length() == 0)
				{
					continue;
				}

				int datatype = header[i].type;
				
				switch(datatype){
				case 2:
					values[i] = stream.transToNumber(values[i]);
					break;
				case 12:
					values[i] = stream.transToDataTime(values[i]);
					break;
				case 100:
					values[i] = stream.transToBinaryFloat(values[i]);
					break;
				case 101:
					values[i] = stream.transToBinaryDouble(values[i]);
					break;
				default:
					continue;
				}
			}
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	std::vector<std::string> values;
	std::vector<int> postPoned;
};
}
}
#endif //__PARSER_ORACLE_PARSER_TNS_ALL_READ_RXD_MESSAGE_H__
