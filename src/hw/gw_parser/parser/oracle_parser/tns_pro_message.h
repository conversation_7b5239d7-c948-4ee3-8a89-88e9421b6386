#ifndef __PARSER_ORACLE_PARSER_TNS_PRO_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_PRO_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class ProRequest{
public:
	bool parseFromStream(SessionData &stream){
		// if (!stream.hasData())
		// {
		// 	return false;
		// }
		// int offset = stream.getOffset();
		try{
			stream.getStringWithEOF(acceptVersion);
			stream.getStringWithEOF(clientPlatform);
			if (clientPlatform.find("Java")!=std::string::npos)
			{
				stream.property.platforms = {0};
				stream.property.codeNumber = {1};
				stream.property.codePtr = {1};
				stream.property.codeString= {1};
			}else if (clientPlatform.find("Linux")!=std::string::npos)
			{				
				stream.property.platforms = {1};
				stream.property.codeNumber = {2};
				stream.property.codePtr = {2};
				stream.property.codeString= {2};
			}else if (clientPlatform.find("WIN")!=std::string::npos)
			{				
				stream.property.platforms = {2};
				stream.property.codeNumber = {2};
				stream.property.codePtr = {2};
				stream.property.codeString= {2};
			}else if (clientPlatform.find("OSX")!=std::string::npos)
			{				
				stream.property.platforms = {2};
				stream.property.codeNumber = {2};
				stream.property.codePtr = {2};
				stream.property.codeString= {2};
			}else if(clientPlatform.find("RS")>=0){				
				stream.property.platforms = {2};
				stream.property.codeNumber = {2};
				stream.property.codePtr = {2};
				stream.property.codeString= {1};
			}
			stream.SetProperty();
#ifdef DEBUG_ORACLE
			printf("ClientPlatform:%d\n", stream.platform);
#endif
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	std::string acceptVersion = "";
	std::string clientPlatform = "";
};

class ProResponse{
public:
	bool isCharsetMutlibyte(int charset){
		switch(charset){
		case 1:
		case 31:
			return false;
		case -5:
		case -1:
		case 870:
		case 871:
		case 873:
			return true;
		default:
			return false;
		}

	}


	bool parseFromStream(SessionData &stream){
		// if (!stream.hasData())
		// {
		// 	return false;
		// }
		// int offset = stream.getOffset();
		try{

			// stream.getStringWithEOF(acceptVersion);
			acceptVersion = stream.unmarshalUB1();
			stream.unmarshalUB1();
			stream.getStringWithEOF(platform);

			serverCharset = stream.getHostShort();
			serverFlags = stream.unmarshalUB1();

			charSetElem = stream.getHostShort();
			if (charSetElem > 0)
			{
				std::string tmpbuf = "";
				stream.getByteBuffer(tmpbuf,charSetElem*5);
			}

			if (acceptVersion<5)
			{
				return true;
			}

			int size = stream.getNetShort();
			std::string tmpstr = "";
			stream.getByteBuffer(tmpstr,size);
			if (size < 7 )
			{
				return true;
			}

			uint8_t inta = tmpstr[5];
			uint8_t intb = tmpstr[6];
			int idx = inta + intb +6;
			if (size< idx + 5)
			{
				return true;
			}

			memcpy(&ncharCharset,tmpstr.data()+idx+3,2);
			if (acceptVersion<6)
			{
				return true;
			}

			size = stream.unmarshalUB1();
			stream.getByteBuffer(serverCompileTimeCap,size);
			if (serverCompileTimeCap.length()>7)
			{
				stream.property.ttcVersions = {serverCompileTimeCap[7]};
			}
			if (serverCompileTimeCap.length()>15 && serverCompileTimeCap[15]==1)
			{
				stream.property.eocsvs = {1};
			}else{
				stream.property.eocsvs = {0};
			}
			stream.SetProperty();
			size = stream.unmarshalUB1();
			stream.getByteBuffer(runtimeCap,size);

			// int8_t serverVer = acceptVersion[0];
			// int defCharset = 0;
			// int s3 = 0;
			// switch(serverVer){
			// case 4:
			// 	defCharset = 7230;
			// 	break;
			// case 5:
			// 	defCharset = 8030;
			// 	break;
			// case 6:
			// 	defCharset = 8100;
			// 	break;
			// }

			// switch(serverCharset){
			// case 1:
			// case 2:
			// case 31:
			// case 178:
			// case 873:
			// 	s3 = serverCharset;
			// 	break;
			// default:
			// 	s3 = (defCharset>8030?871:870);
			// 	break;
			// }

			// if (isCharsetMutlibyte(s3))
			// {
			// 	if (isCharsetMutlibyte(serverCharset))
			// 	{
			// 		stream.setConversionFlags(1);
			// 	}else{
			// 		stream.setConversionFlags(2);
			// 	}
			// }else{
			// 	stream.setConversionFlags(serverFlags);
			// }
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}

		return true;
	}

	int8_t acceptVersion = 0;
	std::string platform = "";
	int serverCharset = 0;
	int clientCharset = 0;
	int serverFlags = 0;
	int charSetElem = 0;
	int16_t ncharCharset = 0;
	std::string serverCompileTimeCap = "";
	std::string runtimeCap = "";
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_PRO_MESSAGE_H__