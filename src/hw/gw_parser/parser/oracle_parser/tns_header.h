#ifndef __PARSER_ORACLE_PARSER_TNS_HEADER_H__
#define __PARSER_ORACLE_PARSER_TNS_HEADER_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class TnsHeader{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		// if (stream.remainLen()<8)
		// {
		// 	return false;
		// }
		try{
			packetLen = stream.getNetShort();
			packetChksm = stream.getNetShort();
			type = stream.unmarshalUB1();
			rsrvd = stream.unmarshalUB1();
			headerChksm = stream.getNetShort();\
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	int packetLen = 0;
	int packetChksm = 0;
	int type = 0;
	int rsrvd = 0;
	int headerChksm = 0;
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_HEADER_H__s