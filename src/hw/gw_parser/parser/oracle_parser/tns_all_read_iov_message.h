#ifndef __PARSER_ORACLE_PARSER_TNS_ALL_READ_IOV_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_ALL_READ_IOV_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class AllReadIOVMessage{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			AllRxhMessage rxh;
			rxh.parseFromStream(stream);
			BindInfo info = stream.getBindInfo(0);
			for (int i = 0; i < info.bindtypes.size(); ++i)
			{
				bvcColSent.push_back(stream.unmarshalUB1());
			}

			stream.outBindValue = bvcColSent;
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	std::vector<uint8_t> bvcColSent;
};

}
}

#endif //__PARSER_ORACLE_PARSER_TNS_ALL_READ_IOV_MESSAGE_H__