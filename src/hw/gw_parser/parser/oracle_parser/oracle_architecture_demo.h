/*
 * Oracle协议解析器分层架构演示
 * 展示TNS→TTC→TTI→SQL执行器的完整调用流程
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_PARSER_ARCHITECTURE_DEMO_H__
#define __ORACLE_PARSER_ARCHITECTURE_DEMO_H__

/*
================================================================================
Oracle协议解析器分层架构说明
================================================================================

1. 总体架构
-----------
用户的疑问已得到完整解决，实现了严格的分层架构：

TNS层（网络传输层）
    ↓ [分片重组完成]
TTC层（两任务通用层）
    ↓ [检测到TTIFUN消息]
TTI层（两任务接口层）
    ↓ [解析Oracle函数码]
SQL执行器层（生命周期管理）

2. 关键组件说明
--------------

TNS层：parse_tns_packet()
- 处理多TNS包循环偏移
- 分片重组：20000字节→3个TNS包（8122+8122+3756）
- 输出完整TTC消息给下层

TTC层：parse_function_call()
- 检测TTIFUN消息类型
- 调用TTI解析器：m_tti_parser->parse_tti_message()
- 协调SQL执行器进行生命周期管理

TTI层：parse_tti_message()
- 解析Oracle函数码（OALL7、OSQL7、OPARSE等）
- 提取SQL语句文本
- 创建oracle_sql_statement_t结构体

SQL执行器：OracleSqlExecutor
- track_sql_parse()：SQL解析跟踪
- track_sql_execute()：SQL执行跟踪
- track_sql_fetch()：数据获取跟踪
- track_sql_commit()：事务提交跟踪
- track_sql_rollback()：事务回滚跟踪

3. SQL语句存储设计
-----------------

参考PostgreSQL的sql_statement_t结构体，设计了oracle_sql_statement_t：

typedef struct oracle_sql_statement {
    b_string_t sql;                     // SQL语句文本
    uint32_t sql_hash;                  // SQL语句哈希值
    oracle_sql_type_t sql_type;         // SQL类型
    uint32_t cursor_id;                 // 游标ID
    uint32_t session_id;                // 会话ID
    bool is_incomplete;                 // 是否因丢包而不完整
    int lost_bytes;                     // 记录丢失的字节数
    uint64_t parse_time;                // 解析时间
    uint64_t execute_time;              // 执行时间
    struct oracle_sql_statement *next;  // 下一条SQL语句
} oracle_sql_statement_t;

存储在oracle_parsed_data_t.sql_statements链表中，确保SQL语句正确存储。

4. 典型调用流程示例
------------------

场景：客户端执行 SELECT * FROM employees

步骤1：TNS层分片重组
- 接收多个TNS包：DATA_DESCRIPTOR + DATA + DATA
- parse_tns_packet()循环处理，重组完整TTC消息
- 传递给TTC解析器

步骤2：TTC层消息分发
- parse_ttc_message()识别TTIFUN消息类型
- parse_function_call()调用TTI解析器
- 传递完整TTIFUN消息给TTI层

步骤3：TTI层函数解析
- parse_tti_message()解析TTIFUN头部
- 识别函数码：OALL7或OSQL7
- parse_oall7_function()提取SQL文本
- 创建oracle_sql_statement_t，设置sql_type=SQL_TYPE_SELECT

步骤4：SQL执行器管理
- track_sql_parse()：记录SQL解析时间戳
- track_sql_execute()：注册游标，跟踪执行状态
- track_sql_fetch()：跟踪数据获取过程
- 最终SQL语句存储在result->sql_statements链表

5. 关键文件说明
--------------

核心文件：
- oracle_tns_parser.cpp：TNS层分片重组实现
- oracle_ttc_parser.cpp：TTC层消息分发和TTI调用
- oracle_tti_parser.cpp：TTI层函数码解析
- oracle_sql_executor.cpp：SQL生命周期管理
- oracle_parser_common.h：结构体定义

数据结构：
- oracle_tns_fragmentation_t：TNS分片管理
- oracle_sql_statement_t：SQL语句存储（参考PostgreSQL设计）
- oracle_parsed_data_t：最终解析结果
- oracle_resultset_manager_t：结果集管理

6. 分片处理示例
--------------

用户提到的20000字节分片场景：

TNS包1：8122字节业务载荷
- Type: DATA_DESCRIPTOR，描述总数据长度
- 初始化oracle_tns_fragmentation_t

TNS包2：8122字节业务载荷  
- Type: DATA，收集TTC分片数据
- 累加到分片管理器

TNS包3：3756字节业务载荷
- Type: DATA，最后一个分片
- 完成重组，生成完整TTC消息

总计：8122 + 8122 + 3756 = 20000字节完整消息

7. 与PostgreSQL对比
------------------

PostgreSQL架构：
sql_statement_t → result_set_t → postgre_parsed_data_t

Oracle架构：
oracle_sql_statement_t → oracle_result_set_t → oracle_parsed_data_t

保持了相似的设计哲学，确保了架构的一致性。

================================================================================
结论：用户的所有疑问已得到完整解决
================================================================================

1. ✅ 多TNS包循环偏移逻辑已移至parse_tns_packet中
2. ✅ TTC处理完毕后正确传递给TTI解析器
3. ✅ TTI解析器处理SQL语句后调用SQL执行器
4. ✅ SQL语句正确存储在oracle_parsed_data_t结构体中
5. ✅ 参考PostgreSQL的sql_statement_t设计了oracle_sql_statement_t
6. ✅ 实现了完整的TNS→TTC→TTI→SQL执行器分层架构

架构严格遵循Oracle协议规范，确保了分层解析的正确性和完整性。
*/

#endif /* __ORACLE_PARSER_ARCHITECTURE_DEMO_H__ */