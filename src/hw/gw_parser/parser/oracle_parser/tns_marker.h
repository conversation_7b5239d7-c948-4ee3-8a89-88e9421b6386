#ifndef __PARSER_ORACLE_PARSER_TNS_MARKER_H__
#define __PARSER_ORACLE_PARSER_TNS_MARKER_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class TnsMarker{
public:
	bool parseFromStream(SessionData &stream){
		// if (stream.remainLen()<3)
		// {
		// 	return false;
		// }

		// int offset = stream.getOffset();
		try{
			marker0 = stream.unmarshalUB1();
			marker1 = stream.unmarshalUB1();
			marker2 = stream.unmarshalUB1();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}

		return true;
	}

	int marker0 = 0;
	int marker1 = 0;
	int marker2 = 0;
};
}
}
#endif //__PARSER_ORACLE_PARSER_TNS_MARKER_H__