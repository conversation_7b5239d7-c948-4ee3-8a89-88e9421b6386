/**
*	会话数据流的封装，用于从数据流中读取各种格式的数据
*
*
*/

#ifndef __PARSER_ORACLE_PARSER_SESSION_DATA__
#define __PARSER_ORACLE_PARSER_SESSION_DATA__

#include <vector>
#include <map>
#include <string>

// #define DEBUG_ORACLE

namespace parser{
namespace oracle{

//缓存的变量绑定信息
struct BindInfo{
	int id = 0;
	std::string sql = "";
	std::vector<int> bindtypes;
};

//返回的结果集列头信息
struct ResultSet{
	int type = 0;
	std::string column_name;
};

struct KeyValueFlag
{
	std::string key = "";
	std::string value = "";
	int flag = 0;
};

enum class ClientPlatform:uint8_t
{
	Jdbc=0,
	Linux,
	Win,
	OSX,
	RS
};

class OverFlow: public std::exception {};

class noncopyable{
protected:
	constexpr noncopyable() = default;
	~noncopyable() = default;
	noncopyable(const noncopyable&) = delete;
	noncopyable& operator= (const noncopyable&) = delete;
};


//以下内容都是影响部分消息格式的部分。在登陆前进行协商。
//对于长连接，抓不到登陆过程时，需要根据消息内容推断。
struct SessionProperty
{
	std::vector<int> platforms={0,1,2};  //# 0 JDBC,1 Linux,2 Win,3 OSX,4 RS,#目前只区分这三种场景
	std::vector<int> ttcVersions={2,3,4,5,6}; //ttc版本，目前只发现这几个
	std::vector<int> protoVersions={313,314};	//协议版本，31---316,目前只能两种区别
	std::vector<int> codeNumber={0,1,2};  //传输数值时的编码方式，0 不编码大字端，1编码，2不编码小字端
	std::vector<int> codePtr={0,1,2};	//传输Ptr数据时的编码方式，同上
	std::vector<int> codeString={1,2}; //传输CHR类型字符串时的编码方式，1不编码，2编码，编码时包含表示长度的字符
	std::vector<int> eocsvs={0,1};		//eosc消息时，是否包含部分内容的开关
	
};

class SessionData: public noncopyable{
public:
	SessionData();




	SessionProperty property;

	void SetPropertyValue(int pf,int tv,int pv,int cn,int cp){
		platform = (ClientPlatform)pf;
		ttcVersion = tv;
		setProtoVersion(pv);
		setTypeRep(1,cn);
		setTypeRep(2,cn);
		setTypeRep(3,cn);
		setTypeRep(4,cp);
	}

	void SetProperty(int pf=0,int tv=0,int pv=0,int cn=0,int cp=0,int cs=0,int es=0){
		platform = (ClientPlatform)(property.platforms[pf]);
		ttcVersion = property.ttcVersions[tv];
		setProtoVersion(property.protoVersions[cn]);

		setTypeRep(0,0);
		setTypeRep(1,property.codeNumber[cn]);
		setTypeRep(2,property.codeNumber[cn]);
		setTypeRep(3,property.codeNumber[cn]);
		setTypeRep(4,property.codePtr[cp]);

		conversionFlags = property.codeString[cs];
		eocsv = property.eocsvs[0];
	}

	void setTypeRep(int idx,int value){
		if (idx>0 && idx<5)
		{
			typerep[idx] = value;
		}
	}

	int getTypeRep(int idx){
		if (idx>0 && idx<5)
		{
			return typerep[idx];
		}
		return 0;
	}

	int getProtoVersion(){
		return protoVersion;
	}

	void setProtoVersion(int version){
		protoVersion = version;
	}

	bool check(int idx,unsigned char* buffer,int buflen){
		if (offset+idx+buflen >data.length() || buffer == nullptr || buflen ==0)
		{
			return false;
		}

		unsigned char * key = (unsigned char*)data.c_str();
		for (int i = 0; i < buflen; ++i)
		{
			if (key[offset+idx+i] !=buffer[i])
			{
				return false;
			}
		}
		return true;
	}

	unsigned char getChar(int idx){
		return (unsigned char)data[offset+idx];
	}

	void remove(int len){
		if (len<1)
		{
			return;
		}

		data.erase(offset,len);
	}


	//消息完整时，返回true，不完整时，返回false
	bool append(const char* buffer,int len);

	void setTTCVersion(int version){
		ttcVersion = version;
	}

	int getTTCVersion(){
		return ttcVersion;
	}

	void setBindInfo(BindInfo info){
		bindinfos[servIP][info.id] = info;
	}

	BindInfo getBindInfo(int id){
		return bindinfos[servIP][id];
	}

	void setConversionFlags(int flag){
		conversionFlags = flag;
	}

	int getConversionFlags(){
		return conversionFlags;
	}

	void setResultSet(std::vector<ResultSet> rs){
		headers = rs;
	}

	std::vector<ResultSet> getResultSet(){
		return headers;
	}

	void clearData();

	bool hasData();

	int remainLen();

	int getOffset();

	void setOffset(int offset);

	int64_t readLongLSB(int8_t size);

	int64_t readLongMSB(int8_t size);

	int64_t buffer2Long(int type);

	int8_t unmarshalSB1();

	uint8_t unmarshalUB1();

	int16_t unmarshalSB2();

	uint16_t unmarshalUB2();

	uint32_t unmarshalUB4();

	int32_t unmarshalSB4();

	int64_t unmarshalSB8();

	uint8_t unmarshalPtr();

	void unmarshalCLRNoLimit(std::string &value);

	void unmarshalCLR(std::string &value,int len);

	void unmarshalCHR(std::string &value,int len);

	int32_t unmarshalSWORD();

	void unmarshalDALC(std::string &value);

	void unmarshalCLRforREFS(std::string &value);

	void getByteBuffer(std::string &value,int len);

	void getStringWithEOF(std::string &value);

	int16_t getHostShort();

	int16_t getNetShort();

	int32_t getNetInt();

	void unmarshalKeyValue(std::vector<KeyValueFlag> &kvs,int size,int flagsize);

	bool isVisibleChar(int idx);
	bool isLetter(int idx);
	static std::string coverUTF16toUTF8(std::string &value);
	static std::string transToNumber(std::string trans);
	static std::string transToBinaryFloat(std::string trans);
	static std::string transToBinaryDouble(std::string trans);
	static std::string transToDataTime(std::string trans);

	//返回行时，与上一行重复的部分会省略，只传不同的部分，这个字符串第几个字符的值，1表示返回该列内容，0表示该列省略。
	std::string bvcValue;

	//存储过程输入输出参数标记，0x20为输入参数,0x10为输出参数
	std::vector<uint8_t> outBindValue;



	//用于解析返回状态以及错误信息.
	//是否包含前导eocsv信息，课通过登陆时的serverCompileTimeCap信息确定
	//长连接无登陆信息时，只能检测判断
	uint8_t eocsv = 0; // 1包含eocsv信息，0 不包含eocsv信息

	ClientPlatform platform;

private:
	//1、2、3、4等字节长度整数的编码类型
	//第一位表示是否压缩，即存储的时候去掉高位的0
	//第二位 0表示大字端，1表示小字端
	int typerep[5];
	//值为1，2，3，4，5，6等
	int ttcVersion;
	//协议版本，300--314
	int protoVersion;

	//编码？
	int conversionFlags;

	uint32_t servIP;
	//<数据库IP，绑定ID，绑定信息>
	static std::map<int,std::map<int,BindInfo>> bindinfos;

	//返回内容时，行数
	int columns;
	std::vector<ResultSet> headers;


	std::string data;
	int offset;

};


	
}
}

#endif //__PARSER_ORACLE_PARSER_SESSION_DATA__