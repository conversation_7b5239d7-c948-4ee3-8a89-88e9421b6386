#include "oracle_parser.h"


#include <openssl/md5.h>

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "gw_i_upload.h"
#include "session_mgt.h"
#include "session.h"

#include "oracle_stream.h"

namespace parser{
namespace oracle{

bool COracleParser::probe(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	if(psm == nullptr || p_stream == nullptr || p_conn == nullptr){
		return false;
	}
	CSession* p_session = psm->find_session(p_conn);
	if(p_session == nullptr){
	    p_session = psm->new_session(p_conn);
	    if (p_session == NULL)
	    {

	      	return false;
	    }
	}


	StreamData *psd;

	if ((psd = p_session->get_stream_data_from_parser(this)) != NULL)
	{
		oracle_stream* pos = psd->p_oracle_stream;
		if (pos){
			if( pos->is_oracle)
			{
				return true;
			}else{
				return false;
			}
		}else{

			return false;
		}
	}

	oracle_stream* pos = new oracle_stream();
	bool check = false;
	for (int i = 0; i < server_list.size(); ++i)
	{
		if (p_conn->server.ipv4 == server_list[i].ipv4 && p_conn->server.port == server_list[i].port)
		{
			check = true;
			break;
		}
	}

	if (!check)
	{

		const char *data;
		int dir = p_stream->dir;
		int data_len;
		int offset_out;
		data = p_session->get_data(this, dir, &data_len, &offset_out);

		if (data == NULL || data_len < 8)
		{
			p_session->discard(this, dir, 0);
			return false;
		}


		if (dir != pos->to_server)
		{
			pos->session_data.clearData();
			pos->to_server = dir;
		}

		if(!pos->session_data.append(data,data_len)){
			delete pos;
			return false;
		}
		if (dir)
		{
			 if(1/*!parse_response(pos)*/){
				delete pos;
				return false;
			}
		}else{
			if(!parse_request(pos) && pos->request.requeststr.length()>0){
				delete pos;
				return false;
			}
		}
	}
	
	psd = new StreamData();
	if (!p_session->set_parser(this, psd))
	{
		delete pos;
	  	delete psd;
	  	return false;
	}

	__sync_fetch_and_add(&m_stats_oracle.cnt_session_total, 1);

    unsigned char id[16];
    char id_str[40] = {0};
    std::string uni_id = "";
    const ConnData* pcon = p_session->get_conn();
    uni_id += std::to_string(p_conn->client.port);
    uni_id += std::to_string(p_conn->client.ipv4);
    uni_id += std::to_string(p_conn->server.port);
    uni_id += std::to_string(p_conn->server.ipv4);
    uni_id += std::to_string(m_comm->gw_time());
    MD5((const unsigned char*)uni_id.c_str(),uni_id.length(),id);
    for(int i=0;i<16;++i){
        sprintf(id_str+(i*2),"%02x",id[i]);
    }
    pos->sessionId = std::string(id_str);
	psd->p_oracle_stream = pos;
	pos->is_oracle = true;
	return true;
}

bool COracleParser::probe_on_close(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	return probe(psm,p_stream,p_conn);
}

bool COracleParser::probe_on_reset(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	return probe(psm,p_stream,p_conn);
}

	
}
}
