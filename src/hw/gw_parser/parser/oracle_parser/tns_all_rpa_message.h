#ifndef __PARSER_ORACLE_PARSER_TNS_ALL_RPA_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_ALL_RPA_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class AllRpaMessage{
public:
	std::vector<int64_t> intArray;
	long long outScn = 0;
	int coursor = 0;
	std::vector<KeyValueFlag> kvs;
	std::string strvaluel = "";

	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			int size = stream.unmarshalUB2();
			for (int i = 0; i < size; ++i)
			{
				intArray.push_back(stream.unmarshalUB4());
			}

			if (size >= 3)
			{				
				outScn = intArray[0] | (intArray[1]<<32);
				coursor = intArray[2];
			}

			size = stream.unmarshalUB2();
			std::string tmpbuf = "";
			stream.getByteBuffer(tmpbuf,size);
			size = stream.unmarshalUB2();
			stream.unmarshalKeyValue(kvs,size,2);

			if (stream.getTTCVersion() >= 4)
			{
				// if (stream.getProtoVersion() == 313)
				// {
				// 	size = stream.unmarshalUB2();
				// }else{	//310,314
				// 	size = stream.unmarshalUB4();
				// }
				// if (size>0)
				// {	
					size = stream.unmarshalUB4();	
					if (size>0)
					{			
						stream.getByteBuffer(strvaluel,size);
					}
				// }
			}
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}

		return true;
	}
};

}
}

#endif  //__PARSER_ORACLE_PARSER_TNS_ALL_RPA_MESSAGE_H__