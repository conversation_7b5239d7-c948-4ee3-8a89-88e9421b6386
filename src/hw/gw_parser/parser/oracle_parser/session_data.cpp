#include "session_data.h"

#include <string.h>
#include <arpa/inet.h>
#include <string>
#include <exception>
// #include <locale>
// #include <codecvt>
#include <boost/locale.hpp>

namespace parser{
namespace oracle{

std::map<int,std::map<int,BindInfo>> SessionData::bindinfos;

SessionData::SessionData():ttcVersion(0),conversionFlags(1),offset(0),protoVersion(0),bvcValue(""){
	int tmpbuf[5]={0,1,1,1,1};
	memcpy(typerep,tmpbuf,sizeof(typerep));
	platform = ClientPlatform::Jdbc;
	data = "";
}

void SessionData::clearData(){
	data = "";
	offset = 0;
}

bool SessionData::hasData(){
	return data.length() > offset;
}

int SessionData::remainLen(){
	return data.length() - offset;
}

int SessionData::getOffset(){
	return offset;
}

void SessionData::setOffset(int offset){
	if (offset<0 || offset>data.length())
	{
		return;
	}
	this->offset = offset;
}


bool SessionData::append(const char* buffer,int len){
	if (offset>0)
	{
		data = data.substr(offset);
		offset = 0;
	}
	data.append(buffer,len);

	unsigned char tmp[] = {0x00,0x00,0x06,0x00,0x00,0x00,0x00,0x00};
	bool result = true;

	//数据有可能通过多个包传送
	int tmpOffset = offset;
	uint32_t nextIdx = 0;
	uint16_t nextLen = 0;
	try{
		do{
			nextLen = getNetShort();
			nextIdx += nextLen;
			if (data.length()==nextIdx)
			{
				if (offset ==0)
				{					
					return true;
				}else{
					break;
				}
			}else if((data.length()-nextIdx>10) && check(0,tmp,8)){
				setOffset(nextIdx);
			}else{				
				setOffset(tmpOffset);
				return false;
			}		
		}while(1);

		if (offset>0)
		{
			setOffset(tmpOffset);
			nextIdx = getNetShort();
			nextLen = 0;
			while(nextIdx<data.length()){
				setOffset(nextIdx);
				nextLen = getNetShort();
				remove(10);
				nextIdx += (nextLen-10);
			}
			result = true;
		}
	}
	catch(OverFlow&){
		result = false;
	}
	offset = tmpOffset;
	return result;
}

int64_t SessionData::readLongLSB(int8_t size){
	int minus = 1;
	if ((size & 0x80) >0 )
	{
		minus = -1;
		size &= 0x7f;
	}

	if(offset+size>data.length()){
		throw OverFlow();
	}

	int64_t value = 0;;
	for (int i = 0; i < size; ++i)
	{
		int64_t tmp = data[offset+i] & 0xff;
		value |= (tmp<<(i*8));
	}
	offset += size;

	return value*minus;
}

int64_t SessionData::readLongMSB(int8_t size){
	int minus = 1;
	if ((size & 0x80) >0 )
	{
		minus = -1;
		size &= 0x7f;
	}

	if(offset+size>data.length()){
		throw OverFlow();
	}

	int64_t value = 0;
	for (int i = 0; i < size; ++i)
	{
		int64_t tmp = data[offset+i] & 0xff;
		value |= (tmp<<((size-i-1)*8));
	}
	offset += size;

	return value*minus;
}

int64_t SessionData::buffer2Long(int type){
	bool compress = (typerep[type]& 0x1)>0?true:false;
	uint8_t size = 0;

	if (!hasData())
	{
		throw OverFlow();
	}

	if (compress)
	{
		size = data[offset];
		offset += 1;
	}else{
		switch(type){
		case 1:
			size = 2;
			break;
		case 2:
			size = 4;
			break;
		case 3:
			size = 8;
			break;
		case 4:
			size = 8;
			break;
		}
	}

	if ((typerep[type] & 0x2)>0 )
	{
		return readLongLSB(size);
	}else{
		return readLongMSB(size);
	}
}

int8_t SessionData::unmarshalSB1(){
	return unmarshalSB2();
}

uint8_t SessionData::unmarshalUB1(){
	if (!hasData())
	{
		throw OverFlow();
	}
	int8_t value = data[offset];
	offset += 1;
	return value;
}

int16_t SessionData::unmarshalSB2(){
	return buffer2Long(1);
}

uint16_t SessionData::unmarshalUB2(){
	return buffer2Long(1);
}

uint8_t SessionData::unmarshalPtr(){
	if ((typerep[4] & 0x1)>0 ){
		return unmarshalUB1();
	}else{
		return buffer2Long(4);
	}
}

uint32_t SessionData::unmarshalUB4(){
	return buffer2Long(2);
}

int32_t SessionData::unmarshalSB4(){
	return buffer2Long(2);
}

int64_t SessionData::unmarshalSB8(){
	return buffer2Long(3);
}

void SessionData::unmarshalCLRNoLimit(std::string &value){
	if (!hasData())
	{
		throw OverFlow();
	}

	value = "";
	uint8_t size = data[offset];
	offset += 1;
	if (size == 0 || size == 253 || size == 255)
	{
		if (size == 253)
		{
			offset+=1;
		}
		return;
	}

	if (size != 254)
	{
		if (offset+size > data.length())
		{
			throw OverFlow();
		}
		value = data.substr(offset,size);
		offset += size;
	}else{
		while(hasData()){
			size = data[offset];
			offset += 1;
			if (size == 0)
			{
				break;
			}
			if (offset+size>data.length())
			{
				throw OverFlow();
			}
			value.append(data.substr(offset, size));
			offset += size;
		}
	}
}

void  SessionData::unmarshalCLR(std::string &value,int len){
	if (!hasData())
	{
		throw OverFlow();
	}
	value = "";
	uint8_t size = data[offset];
	offset += 1;
	if (size == 0 || size == 253 || size == 255)
	{
		return;
	}

	if (size != 254)
	{
		size = (size>len)?len:size;
		if ((offset+size)>data.length())
		{
			throw OverFlow();
		}
		value = data.substr(offset,size);
		offset += size;
	}else{
		while(hasData()){
			size = data[offset];
			offset += 1;
			if (size == 0)
			{
				break;
			}

			size = (size>len)?len:size;
			if ((offset+size)>data.length())
			{
				throw OverFlow();
			}
			value.append(data.substr(offset,size));
			len -= size;
			offset += size;
		}
	}

}

void SessionData::unmarshalCHR(std::string &value,int len){
	if (property.codeString.size()>0)
	{
		if (!isLetter(0))
		{
			property.codeString = {2};
		}else{
			char* start= (char*)data.c_str();
			start += offset;
			if (strncasecmp(start,"select",6) ==0 ||
				strncasecmp(start,"insert",6) ==0 ||
				strncasecmp(start,"update",6) ==0 ||
				strncasecmp(start,"delete",6) ==0 ||
				strncasecmp(start,"begin",5) ==0 ||
				strncasecmp(start,"create",6) ==0)
			{
				property.codeString = {1};
			}
		}
		conversionFlags = property.codeString[0];
	}

	if ((conversionFlags & 0x2) >0)
	{
		unmarshalCLR(value,len);
	}else{
		getByteBuffer(value,len);
	}
}

int32_t SessionData::unmarshalSWORD(){
	return unmarshalUB4();
}

void  SessionData::unmarshalDALC(std::string &value){
	int len = unmarshalUB4();
	if (len >0)
	{
		unmarshalCLR(value,len);
	}
}

void SessionData::unmarshalCLRforREFS(std::string &value){
	if (!hasData())
	{
		throw OverFlow();
	}
	value = "";
	uint8_t size = data[offset];
	offset += 1;
	if (size == 0 || size == 253 || size == 255)
	{
		return;
	}

	if (size == 254)
	{
		int tmpsize = 0;
		while(hasData()){
			tmpsize = data[offset];
			offset += 1;
			if (tmpsize <= 0)
			{
				break;
			}
			if (tmpsize != 254)
			{				
				std::string tmpstr= "";
				getByteBuffer(tmpstr,tmpsize);
				value.append(tmpstr);
			}
		}
	}
	std::string tmpstr = "";
	getByteBuffer(tmpstr,size);
	value.append(tmpstr);
}

void SessionData::getByteBuffer(std::string &value,int len){
	if (!hasData() || len < 0 || offset+len > data.length())
	{
		throw OverFlow();
	}
	value = data.substr(offset,len);
	offset += len;
}

void SessionData::getStringWithEOF(std::string &value){
	int idx = 0;
	if (!hasData())
	{
		return;
	}
	for (idx = offset; idx < data.length(); )
	{
		if (data[idx++] == 0)
		{
			break;
		}
	}
	value = data.substr(offset,idx-offset);
	offset = idx;
}

int16_t SessionData::getHostShort(){
	if (offset+2>= data.length())
	{
		throw OverFlow();
	}
	int16_t value=0;
	const char* buffer = data.c_str();
	memcpy(&value,buffer+offset,sizeof(int16_t));
	offset += 2;
	return value;
}

int16_t SessionData::getNetShort(){
	return ntohs(getHostShort());
}

int32_t SessionData::getNetInt(){
	if (offset+4>data.length())
	{
		throw OverFlow();
	}
	int32_t value = 0;
	const char* buffer = data.c_str();
	memcpy(&value,buffer+offset,sizeof(int32_t));
	return ntohl(value);
}

void SessionData::unmarshalKeyValue(std::vector<KeyValueFlag> &kvs,int size,int flagsize){
	for (int i = 0; i < size; ++i)
	{
		int keysize = 0, valuesize = 0;
		KeyValueFlag kv;
		keysize = unmarshalSB4();
		if (keysize > 0)
		{
			unmarshalCLR(kv.key,keysize);
		}
		valuesize = unmarshalSB4();
		if (valuesize > 0)
		{
			unmarshalCLR(kv.value,valuesize);
		}
		if (flagsize == 2)
		{
			kv.flag = unmarshalSB2();
		}else if(flagsize == 4){
			kv.flag = unmarshalSB4();
		}

		kvs.push_back(kv);
	}
}

bool SessionData::isVisibleChar(int idx){
	if (offset+idx>= data.length())
	{
		return false;
	}
	unsigned char key = data[offset+idx];
	return (key >=0x20) && (key<=0x7e);
}

bool SessionData::isLetter(int idx){
	if (offset+idx>= data.length())
	{
		return false;
	}	
	unsigned char key = data[offset+idx];
	return ((key >=0x41) && (key<=0x5a)) ||((key >=0x61) && (key<=0x7a));
}

std::string SessionData::coverUTF16toUTF8(std::string &value){
	// static std::wstring_convert<std::codecvt_utf16<wchar_t>> strCnv;
	// static std::wstring_convert<std::codecvt_utf8<wchar_t>> strCnv2;

	// std::wstring tmp = strCnv.from_bytes(value);
	// return strCnv2.to_bytes(tmp);
	return boost::locale::conv::between(value,"UTF-16","UTF-8");
}



std::string SessionData::transToNumber(std::string trans){
	long double value = 0;
	int8_t u0 = trans[0];
	if (u0 == 0x80)
	{
		return "";
	}else if((u0 & 0x80) == 0){
		uint8_t ux = trans.back();
		if (ux != 102)
		{
			return "";
		}
		for (int i = 1; i < trans.length()-1; ++i)
		{
			uint8_t un = trans[i];
			value = value *100 +(101-un);
		}

		int8_t pow = 63-u0-(trans.length()-1)+1;
		if(pow > 0)
		{
			while(pow>0){
				value = value*100;
				pow--;
			}
		}else{
			while(pow<0){
				value= value/100.0;
				pow++;
			}
		}
		value *= -1;
	}else{
		int size = u0 & 0x3f;
		for (int i = 1; i < trans.length(); ++i)
		{
			uint8_t un = trans[i];
			value = value*100+(un-1);
		}
		int8_t pow = u0-192-trans.length()+1;
		if (pow > 0)
		{
			while(pow>0){
				value = value*100;
				pow--;
			}
		}else{
			while(pow<0){
				value= value/100.0;
				pow++;
			}
		}
	}

	return std::to_string(value);
}



std::string SessionData::transToBinaryFloat(std::string trans){
	if (trans.length()<4)
	{
		return "";
	}
	uint8_t u[4]={0};
	u[0] = trans[0];
	u[1] = trans[1];
	u[2] = trans[2];
	u[3] = trans[3];

	if ((u[0] & 0x80)!=0)
	{
		u[0] &= 0x7F;
		u[1] &= 0x7F;
		u[2] &= 0x7F;
		u[3] &= 0x7F;
	}else{
		u[0] ^= 0xFF;
		u[1] ^= 0xFF;
		u[2] ^= 0xFF;
		u[3] ^= 0xFF;
	}

	return std::to_string(*(float*)u);
}

std::string SessionData::transToBinaryDouble(std::string trans){		
	if (trans.length()<8)
	{
		return "";
	}
	uint8_t u[8]={0};
	u[0] = trans[0];
	u[1] = trans[1];
	u[2] = trans[2];
	u[3] = trans[3];
	u[4] = trans[4];
	u[5] = trans[5];
	u[6] = trans[6];
	u[7] = trans[7];

	if ((u[0] & 0x80)!=0)
	{
		u[0] &= 0x7F;
		u[1] &= 0x7F;
		u[2] &= 0x7F;
		u[3] &= 0x7F;
		u[4] &= 0x7F;
		u[5] &= 0x7F;
		u[6] &= 0x7F;
		u[7] &= 0x7F;
	}else{
		u[0] ^= 0xFF;
		u[1] ^= 0xFF;
		u[2] ^= 0xFF;
		u[3] ^= 0xFF;
		u[4] ^= 0xFF;
		u[5] ^= 0xFF;
		u[6] ^= 0xFF;
		u[7] ^= 0xFF;
	}

	return std::to_string(*(double*)u);
}


std::string SessionData::transToDataTime(std::string trans){
	if (trans.length()<7)
	{
		return "";
	}
	int u[7] = {0};
	u[0] = trans[0];
	u[1] = trans[1];
	u[2] = trans[2];
	u[3] = trans[3];
	u[4] = trans[4];
	u[5] = trans[5];
	u[6] = trans[6];
	int year = (u[0]-100)*100+(u[1]-100);
	std::stringstream ss;
	ss<<year<<"-"<<u[2]<<"-"<<u[3]<<" "<<u[4]<<":"<<u[5]<<":"<<u[6];
	return ss.str();
}


}
}