#ifndef __PARSER_ORACLE_PARSER_TNS_ALL_RXH_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_ALL_RXH_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class AllRxhMessage{
public:

	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			flg = stream.unmarshalUB1();
			numRqsts = stream.unmarshalUB2();
			iterNum = stream.unmarshalUB2();
			numRqsts += iterNum*256;
			numItersThisTime = stream.unmarshalUB2();
			uacBufLength = stream.unmarshalUB2();
			if (stream.platform == ClientPlatform::Linux)
			{
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				int size = stream.unmarshalUB2();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
				stream.unmarshalUB2();

				if (size > 0)
				{
					std::string tmpbuf = "";
					stream.getByteBuffer(tmpbuf,size);
					for (int i = 0; i < tmpbuf.length(); ++i)
					{
						uint8_t key = tmpbuf[i];
						for (int j = 0; j < 8; ++j)
						{
							if ((key & (1<<j)) !=0)
							{
								bvcColSent += "1";
							}else{
								bvcColSent += "0";
							}
						}
					}
				}	
			}else{		
				if (stream.platform >= ClientPlatform::Win)
				{
					stream.unmarshalUB4();
				}		
				std::string tmpbuf = "";
				stream.unmarshalDALC(tmpbuf);
				for (int i = 0; i < tmpbuf.length(); ++i)
				{
					uint8_t key = tmpbuf[i];
					for (int j = 0; j < 8; ++j)
					{
						if ((key & (1<<j)) !=0)
						{
							bvcColSent += "1";
						}else{
							bvcColSent += "0";
						}
					}
				}
				std::string tmpbuf2= "";
				stream.unmarshalDALC(tmpbuf2);
			}

			stream.bvcValue = bvcColSent;
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	int flg = 0;
	int numRqsts = 0;
	int iterNum = 0;
	int numItersThisTime = 0;
	int uacBufLength = 0;
	std::string bvcColSent = "";
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_ALL_RXH_MESSAGE_H__