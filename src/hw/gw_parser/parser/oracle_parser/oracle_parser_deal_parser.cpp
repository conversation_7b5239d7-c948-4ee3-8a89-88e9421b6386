#include "oracle_parser.h"


#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include<sys/time.h>

#include <openssl/md5.h>

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "gw_i_upload.h"
#include "session_mgt.h"
#include "session.h"

#include "cJSON.h"

#include "session_data.h"
#include "oracle_stream.h"

#include "tns_accept.h"
#include "tns_header.h"
#include "tns_all_message.h"
#include "tns_all_read_bvc_message.h"
#include "tns_all_read_rxd_message.h"
#include "tns_all_rpa_message.h"
#include "tns_all_rxh_message.h"
#include "tns_authenticate_message.h"
#include "tns_command.h"
#include "tns_connect.h"
#include "tns_data.h"
#include "tns_datatype_representation_message.h"
#include "tns_dcb_message.h"
#include "tns_eosc_message.h"
#include "tns_marker.h"
#include "tns_oac_message.h"
#include "tns_pro_message.h"
#include "tns_redirect.h"
#include "tns_refuse.h"
#include "tns_rxd_message.h"
#include "tns_uds_message.h"
#include "tns_version_message.h"
#include "tns_close_message.h"
#include "tns_commoncall_message.h"
#include "tns_oses_message.h"
#include "tns_all_read_iov_message.h"

static const char msg_event_type[] = "oracle_event";
static const char msg_content_type[] = "oracle_content";

namespace parser{
namespace oracle{
/**
* 在接收数据时，解析数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
int COracleParser::parse(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	if(psm == nullptr || p_stream == nullptr || p_conn == nullptr){
		return false;
	}
	CSession* p_session = psm->find_session(p_conn);
	if(p_session == nullptr){
		return -1;
	}


	StreamData *psd;

	if ((psd = p_session->get_stream_data_from_parser(this)) == NULL)
	{
		return -1;
	}
	const char *data;
	int dir = p_stream->dir;
	int data_len;
	int offset_out;
	data = p_session->get_data(this, dir, &data_len, &offset_out);
	if (data_len < 1)
	{
		return -1;
	}

	oracle_stream* pos = psd->p_oracle_stream;

	if (dir != pos->to_server)
	{
		pos->session_data.clearData();
		pos->to_server = dir;
	}


	if(!pos->session_data.append(data,data_len)){
		return 0;
	}

	if (dir)
	{
		__sync_fetch_and_add(&m_stats_oracle.cnt_parser_response, 1);
		if (parse_response(pos))
		{
			if (pos->hasRequest())
			{				
				upload_event(pos,p_conn);
			}

			if(pos->hasHeader()){				
				upload_response(pos);
			}

			if (pos->hasContent())
			{				
				upload_response(pos);
			}
			pos->reset();
		}
	}else{
		__sync_fetch_and_add(&m_stats_oracle.cnt_parser_request, 1);		
		
		// if (pos->hasRequest())
		// {				
		// 	upload_event(pos,p_conn);
		// }
		// pos->reset();
		parse_request(pos);
	}
  	return 0;
}


int COracleParser::parse_clear(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	return 0;
}

/**
* 在连接关闭时，解析数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
int COracleParser::parse_on_close(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	__sync_fetch_and_add(&m_stats_oracle.cnt_session_closed, 1);
	return parse(psm,p_stream,p_conn);
}

/**
* 在连接重置时，解析数据流协议。
* @param CSessionMgt *
* @param app_stream *
* @param struct conn *
*/
int COracleParser::parse_on_reset(CSessionMgt *psm, const app_stream *p_stream, const struct conn *p_conn){
	__sync_fetch_and_add(&m_stats_oracle.cnt_session_closed, 1);
	return parse(psm,p_stream,p_conn);
}

bool COracleParser::parse_request(oracle_stream* pos){
	int offset = pos->session_data.getOffset();
	bool hasrequest = false;
	// try{

	    struct timeval tv;  
	    gettimeofday(&tv,NULL);
	    pos->request.tm = tv.tv_sec;
	    pos->request.tm = pos->request.tm * 1000 + tv.tv_usec/1000;

		TnsHeader header;
		header.parseFromStream(pos->session_data);

		switch(header.type){
		case 1:{
			TnsConnect connect;
			connect.parseFromStream(pos->session_data);
			pos->request.requeststr = connect.connectStr;
			hasrequest = true;
			break;
		}
		case 12:{
			TnsMarker marker;
			marker.parseFromStream(pos->session_data);
			break;
		}
		case 6:{
			TnsData data;
			data.parseFromStream(pos->session_data);
			if (data.dataFlag == 0x0040)
			{
				//end session
				pos->session_data.clearData();
				break;
			}
			pos->session_data.setOffset(offset + 10);
			while(pos->session_data.hasData()){
				TnsCommand command;
				command.parseFromStream(pos->session_data);
#ifdef DEBUG_ORACLE
				printf("request::%02x,%02x\n",command.cmdId,command.callId );
#endif
				pos->preCallId = command.callId;
				switch(command.cmdId){
				case 0:{
					//解析失败
					pos->session_data.clearData();
					return hasrequest;
				}
				case 0x01:{
					ProRequest pro;
					pro.parseFromStream(pos->session_data);
					pos->client_platform = pro.clientPlatform;
					break;
				}
				case 0x02:{
					DataTypeRepresentationRequest dtr;
					dtr.parseFromStream(pos->session_data);
					break;
				}
				case 0x03:{
					switch(command.callId){
					// case 0x76:
					case 0x73:{
						// printf("command:%02x %02x\n", command.cmdId,command.callId);
						AuthenticateRequest auth;
						auth.parseFromStream(pos->session_data);
						pos->user_name = auth.userNmae;
						pos->request.requeststr = "login";
						for (int i = 0; i < auth.kvs.size(); ++i)
						{
							KeyValueFlag &kv = auth.kvs[i];
#ifdef DEBUG_ORACLE
							printf("key:%s,,,value:%s\n", kv.key.c_str(),kv.value.c_str());
#endif
							if (kv.key.find("SESSION_CLIENT_DRIVER_NAME")!=std::string::npos
								|| kv.key.find("AUTH_PROGRAM_NM")!=std::string::npos)
							{
								pos->client_tool =kv.value;
								break;
							}
						}
						hasrequest = true;
						break;
					}
					case 0x3b:{
						VersionRequest vq;
						vq.parseFromStream(pos->session_data);
						break;
					}
					case 0x5e:
					case 0x4e:
					case 0x05:{
						AllMessage all(command.callId);
						all.parseFromStream(pos->session_data);
						pos->request.requeststr = all.sql;
#ifdef DEBUG_ORACLE
						printf("sql:%s\n", all.sql.c_str());
#endif
						for (int i = 0; i < all.values.size(); ++i)
						{
							pos->request.requeststr += " :";
							pos->request.requeststr += std::to_string(i+1);
							pos->request.requeststr += "=";
							pos->request.requeststr += all.values[i];
						}
						if (all.sql.length()>0)
						{							
							hasrequest = true;
						}
						break;
					}
					case 0x09:
					case 0x0e:
					case 0x0f:{
						CommonCallMessage commoncall;
						commoncall.parseFromStream(pos->session_data);
						break;
					}
					default:{
						pos->session_data.clearData();
						return hasrequest;
					}
					}
					break;
				}
				case 0x11:{
					switch(command.callId){
					case 0x6b:{
						OsesMessage oses;
						oses.parseFromStream(pos->session_data);
						break;
					}
					case 0x78:
					case 0x69:{
						ClsoeMessage close;
						close.parseFromStream(pos->session_data);
						break;
					}
					}
					break;
				}
				default:{
					pos->session_data.clearData();
					return hasrequest;
				}
				}
			}
			break;
		}
		default:{
			pos->session_data.clearData();
			return hasrequest;
		}
		}
// 	}catch(OverFlow&){
// #ifdef DEBUG_ORACLE
// 		printf("request offset:%d\n", offset);
// #endif
// 		pos->session_data.setOffset(offset);
// 		return hasrequest;
// 	}

	return hasrequest;
}


bool COracleParser::parse_response(oracle_stream* pos){
	int offset = pos->session_data.getOffset();
	bool hascontent = false;
	// try{

	    struct timeval tv;  
	    gettimeofday(&tv,NULL);
	    pos->response.tm = tv.tv_sec;
	    pos->response.tm = pos->response.tm * 1000 + tv.tv_usec/1000;

		TnsHeader header;
		header.parseFromStream(pos->session_data);
		switch(header.type){
		case 2:{
			TnsAccept accept;
			accept.parseFromStream(pos->session_data);
			// pos->server_version = accept.version;
			pos->response.responsestr = accept.acceptData;
			hascontent = true;
			break;
		}
		case 4:{
			TnsRefuse refuse;
			refuse.parseFromStream(pos->session_data);
			pos->response.relcode = refuse.userRefuseReason > 0 ? refuse.userRefuseReason: refuse.sysResuseReason;
			pos->response.errorMsg = refuse.refuseStr;
			hascontent = true;
			break;
		}
		case 5:{
			TnsRedirect redirect;
			redirect.parseFromStream(pos->session_data);
			pos->response.responsestr = redirect.redirectStr;
			hascontent = true;
			break;
		}
		case 12:{
			TnsMarker marker;
			marker.parseFromStream(pos->session_data);
			break;
		}
		case 6:{
			TnsData data;
			data.parseFromStream(pos->session_data);
			if (data.dataFlag == 0x0040)
			{
				//end session
				pos->session_data.clearData();
				break;
			}
			pos->session_data.setOffset(offset+10);
			while(pos->session_data.hasData()){
				TnsCommand command;
				command.parseFromStream(pos->session_data);
#ifdef DEBUG_ORACLE
				printf("response:%02x,%02x\n",command.cmdId,command.callId );
#endif
				switch(command.cmdId){
				case 0:{
					//解析失败
					pos->session_data.clearData();
					return hascontent;
				}
				case 0x01:{
					ProResponse pro;
					pro.parseFromStream(pos->session_data);
					pos->server_platform = pro.platform;
					break;
				}
				case 0x02:{
					break;
				}
				case 0x04:{
					EOCSReponse4::CheckMessage(pos->session_data);
					EOCSReponse4 eocs;
					eocs.parseFromStream(pos->session_data);
					pos->response.relcode = eocs.retcode;
					pos->response.errorMsg = eocs.errorMsg;
					hascontent = true;
					break;
				}
				case 0x09:{
					EOCSReponese9 eocs;
					eocs.parseFromStream(pos->session_data);
					break;
				}
				case 0x10:{
					DcbMessage::CheckMessage(pos->session_data);
					DcbMessage dcb;
					dcb.parseFromStream(pos->session_data);
					pos->response.column_headers.clear();
					for (int i = 0; i < dcb.headers.size(); ++i)
					{
						pos->response.column_headers.push_back(dcb.headers[i].column_name);
					}
					hascontent = true;
					break;
				}
				case 0x06:{
					AllRxhMessage rxh;
					rxh.parseFromStream(pos->session_data);
					break;
				}
				case 0x07:{
					AllReadRxdMessage rxd;
					rxd.parseFromStream(pos->session_data);

					if ((pos->session_data.bvcValue.length()>0) && (pos->response.rows.size()>0))
					{
						std::vector<std::string> lastrow = pos->response.rows.back();
						if ((rxd.values.size() == pos->session_data.bvcValue.length()) && (rxd.values.size() == lastrow.size()))
						{
							for (int i = 0; i < pos->session_data.bvcValue.length(); ++i)
							{
								if (pos->session_data.bvcValue[0] == '0')
								{
									rxd.values[i] = lastrow[i];
								}
							}
						}
					}
					pos->session_data.bvcValue = "";
					pos->response.rows.push_back(rxd.values);
#ifdef DEBUG_ORACLE
					printf("rows:%d\n",pos->response.rows.size());
#endif
					if (pos->response.rows.size()>0)
					{
						hascontent = true;
					}
					break;
				}
				case 0x15:{
					AllReadBvcMessage bvc;
					bvc.parseFromStream(pos->session_data);
					break;
				}
				case 0x0b:{
					AllReadIOVMessage ovi;
					ovi.parseFromStream(pos->session_data);
					break;
				}
				case 0x08:{
					switch(pos->preCallId){
					// case 0x76:
					case 0x73:{
						AuthenticateResponse auth;
						auth.parseFromStream(pos->session_data);

						for (int i = 0; i < auth.kvs.size(); ++i)
						{
							KeyValueFlag &kv = auth.kvs[i];
							if (kv.key.find("AUTH_SC_DBUNIQUE_NAME")!=std::string::npos)
							{
								pos->dbname =kv.value;
								break;
							}
						}
						hascontent = true;
						break;
					}
					case 0x3b:{
						VersionResponse vr;
						vr.parseFromStream(pos->session_data);
						pos->server_version = vr.rdbmsVersion;
						break;
					}
					case 0x05:
					case 0x4e:
					case 0x5e:{
						AllRpaMessage rpa;
						rpa.parseFromStream(pos->session_data);
						hascontent = true;
						break;
					}
					default:{
						pos->session_data.clearData();
						return hascontent;
					}
					}
					break;
				}
				default:{
					pos->session_data.clearData();
					return hascontent;
				}
				}
			}
		}
		default:{
			pos->session_data.clearData();
			return hascontent;
		}
		}
// 	}catch(OverFlow&){
// #ifdef DEBUG_ORACLE
// 		printf("setOffset:%d\n", offset);
// #endif
// 		pos->session_data.setOffset(offset);
// 		return hascontent;
// 	}

	return hascontent;
}

void COracleParser::add_net_json(cJSON *p_json_obj, oracle_stream* pos, const struct conn *pcon)
{
    if (p_json_obj == NULL || pcon == NULL)
    {
        return;
    }

    char a_src_ip[64] = {0};
    char a_dst_ip[64] = {0};

    if (pcon->client.v == 4)
    {
        strncpy(a_src_ip, int_ntoa(pcon->client.ipv4), 64 - 1);
        strncpy(a_dst_ip, int_ntoa(pcon->server.ipv4), 64 - 1);
    }
    else
    {
        get_ip6addr_str((uint32_t*)pcon->client.ipv6, a_src_ip, COUNTOF(a_src_ip));
        get_ip6addr_str((uint32_t*)pcon->server.ipv6, a_dst_ip, COUNTOF(a_dst_ip));
    }


    unsigned char id[16];
    char id_str[40] = {0};
    std::string uni_id = "";
    uni_id += a_src_ip;
    uni_id += a_dst_ip;
    uni_id += std::to_string(pcon->client.port);
    uni_id += std::to_string(pcon->server.port);
    uni_id += std::to_string(pos->request.tm);
    MD5((const unsigned char*)uni_id.c_str(),uni_id.length(),id);
    for(int i=0;i<16;++i){
        sprintf(id_str+(i*2),"%02x",id[i]);
    }
    pos->req_md5=std::string(id_str);
    cJSON_AddStringToObject(p_json_obj, "id", id_str);

    cJSON_AddStringToObject(p_json_obj, "ip", a_src_ip);
    cJSON_AddNumberToObject(p_json_obj, "port", pcon->client.port);
    cJSON_AddStringToObject(p_json_obj, "serverIp", a_dst_ip);
    cJSON_AddNumberToObject(p_json_obj, "serverPort", pcon->server.port);



    return;
}

void COracleParser::upload_event(oracle_stream* pos,const struct conn *p_conn){
    cJSON* root = cJSON_CreateObject();
    
    add_net_json(root,pos,p_conn);

    cJSON_AddStringToObject(root, "username", pos->user_name.c_str());
    // cJSON_AddStringToObject(root, "version", pos->client_version.c_str());

    cJSON_AddStringToObject(root, "currentDB", pos->dbname.c_str());
    cJSON_AddNumberToObject(root, "timestamp", pos->request.tm);
    cJSON_AddStringToObject(root, "reqContent", pos->request.requeststr.c_str());
    cJSON_AddNumberToObject(root, "success", (pos->response.relcode == 0) || (pos->response.relcode== 1403));
    // cJSON_AddStringToObject(root, "operation", request.request_method.c_str());
    cJSON_AddNumberToObject(root, "errCode", pos->response.relcode);
    if (pos->response.relcode>0)
    {    	
    	cJSON_AddStringToObject(root, "errMsg", pos->response.errorMsg.c_str());
    }
	cJSON_AddStringToObject(root, "sessionId", pos->sessionId.c_str());
    cJSON_AddNumberToObject(root, "resourceType", RESOURCE_TYPE_ORACLE);
    cJSON_AddStringToObject(root,"serverVersion",pos->server_version.c_str());
    uint64_t tiemlen = pos->response.tm-pos->request.tm;
    tiemlen= (tiemlen>0)?tiemlen:0;
    cJSON_AddNumberToObject(root, "reqTime", tiemlen);
    cJSON_AddStringToObject(root,"clientType",pos->client_tool.c_str());
    
    char *p_log_str = cJSON_PrintUnformatted(root);
    if(p_log_str){
        upload_msg(p_log_str,msg_event_type);
        __sync_fetch_and_add(&m_stats_oracle.cnt_sql, 1);
    }
    pos->request.requeststr = "";
    cJSON_Delete(root);
}

void COracleParser::upload_response(oracle_stream* pos){
    cJSON* root = cJSON_CreateObject();


    cJSON_AddStringToObject(root, "eventId", pos->req_md5.c_str());
    cJSON_AddNumberToObject(root, "timestamp", pos->request.tm);
    // cJSON_AddStringToObject(root, "operation", pos->request.request_method.c_str());
    // if (pos->response.responsestr.length()>0)
    // {    	
    // 	cJSON_AddStringToObject(root, "responseBody", pos->response.responsestr.c_str());
    // }else 
    if(pos->response.column_headers.size()>0){
    	cJSON *p_res_json = cJSON_CreateObject();
    	cJSON *p_column_array = cJSON_CreateArray();

    	for (int i = 0; i < pos->response.column_headers.size(); ++i)
    	{
    		cJSON *p_str_json = cJSON_CreateString(pos->response.column_headers[i].c_str());
    		cJSON_AddItemToArray(p_column_array, p_str_json);
    	}
    	cJSON_AddItemToObject(p_res_json, "columns", p_column_array);
    	cJSON_AddItemToObject(root, "responseBody", p_res_json);

    	cJSON_AddNumberToObject(root, "requestType", 4);//响应列头

    	pos->response.column_headers.clear();
    }else if(pos->response.rows.size()>0){
    	cJSON *p_res_json = cJSON_CreateObject();
    	cJSON *p_rows = cJSON_CreateArray();
    	for (int i = 0; i < pos->response.rows.size(); ++i)
    	{
    		cJSON *p_row = cJSON_CreateArray();
    		std::vector<std::string>& row = pos->response.rows[i];
    		for (int i = 0; i < row.size(); ++i)
    		{    			
	    		cJSON *p_str_json = cJSON_CreateString(row[i].c_str());
	    		cJSON_AddItemToArray(p_row, p_str_json);
    		}
    		cJSON_AddItemToArray(p_rows, p_row);

    	}
    	cJSON_AddItemToObject(p_res_json, "columnValues", p_rows);
    	cJSON_AddItemToObject(root, "responseBody", p_res_json);
    	cJSON_AddNumberToObject(root, "requestType", 5);//响应值
    	pos->response.rows.clear();
    }
    cJSON_AddNumberToObject(root, "effectRows", pos->response.row_num);
    cJSON_AddNumberToObject(root, "resourceType", RESOURCE_TYPE_ORACLE);
    char* p_log_str = cJSON_PrintUnformatted(root);
    if(p_log_str){
        upload_msg(p_log_str,msg_content_type);
    }
    cJSON_Delete(root);

}

void COracleParser::upload_msg(const char *content,const char* type)
{
    if (m_pUpload == NULL)
    {
        GWLOG_INFO(m_comm, "%s upload null\n", ORACLE_LOG_PRE);
        return;
    }

    UploadMsg *pum = new UploadMsg;
    memset(pum, 0, sizeof(UploadMsg));

    // hive_upload_user_data_t *p_ud = (hive_upload_user_data_t *)malloc(sizeof(hive_upload_user_data_t));
    // memset(p_ud, 0, sizeof(hive_upload_user_data_t));
    // if (op_id)
    // {
    //     p_ud->op_id = strdup(op_id);
    //     p_ud->op_id_len = strlen(op_id);
    // }
    // p_ud->event_type = evt_type;

    pum->cb = sizeof(UploadMsg);
    pum->destroy_func = free_upload_msg;
    // pum->userdata = p_ud;
    pum->parser = this;
    pum->length = strlen(content);
    pum->s = content;
    pum->msgtype = type;
    pum->mem_size = sizeof(UploadMsg) + pum->length;

    m_pUpload->put_msg(pum);
}

void COracleParser::free_upload_msg(const struct UploadMsg *p_um)
{
    if (p_um)
    {
        if (p_um->s != NULL)
        {
            cJSON_free((void *)p_um->s);
        }

        // hive_upload_user_data_t *p_ud = (hive_upload_user_data_t *)p_um->userdata;
        // if (p_ud)
        // {
        //     if (p_ud->op_id)
        //     {
        //         free(p_ud->op_id);
        //     }
        //     free(p_ud);
        // }
        delete p_um;
    }

    // __sync_fetch_and_sub(&g_stats_queue_memory_size, length);
}
}
}