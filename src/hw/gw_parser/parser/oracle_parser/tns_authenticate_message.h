#ifndef __PARSER_ORACLE_PARSER_TNS_AUTHENTICATE_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_AUTHENTICATE_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class AuthenticateRequest{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			seqNumber = stream.unmarshalUB1();
			stream.unmarshalPtr();
			int userlen = stream.unmarshalSB4();
			logonMode = stream.unmarshalUB4();
			stream.unmarshalPtr();
			int kvsize = stream.unmarshalUB4();
			if (stream.platform == ClientPlatform::Linux)
			{
				stream.unmarshalUB4();
			}
			stream.unmarshalPtr();
			stream.unmarshalPtr();


#ifdef DEBUG_ORACLE
			printf("userlen:%d,kvsize:%d\n",userlen,kvsize );
#endif
			if (userlen > 0)
			{
				stream.unmarshalCHR(user<PERSON><PERSON><PERSON>,userlen);
			}
			
			if (kvsize > 0)
			{
				stream.unmarshalKeyValue(kvs,kvsize,4);
			}
			stream.clearData();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}

		return true;
	}

	int seqNumber = 0;
	int logonMode = 0;
	std::string userNmae = "";
	std::vector<KeyValueFlag> kvs;
};

class AuthenticateResponse{
public:
	bool parseFromStream(SessionData &stream){
		try{

			int kvsize = stream.unmarshalUB2();

			stream.unmarshalKeyValue(kvs,kvsize,4);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	std::vector<KeyValueFlag> kvs;
};
}
}


#endif  //__PARSER_ORACLE_PARSER_TNS_AUTHENTICATE_MESSAGE_H__