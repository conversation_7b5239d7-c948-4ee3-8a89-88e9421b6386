#ifndef __PARSER_ORACLE_PARSER_ORACLE_STREAM_H__
#define __PARSER_ORACLE_PARSER_ORACLE_STREAM_H__

#include "session_data.h"

// namespace parser{
// namespace oracle{

struct RequestInfo
{
	std::string requeststr;
	int64_t tm;

    void reset(){
        requeststr = "";
        tm = 0;
    }
};

struct ResponseInfo
{
	std::string responsestr;
	int relcode;
    std::string errorMsg;
	std::vector<std::string> column_headers;
	std::vector<std::vector<std::string> > rows;
	int row_num;
	int64_t tm;

    void reset(){
        responsestr = "";
        relcode = 0;
        errorMsg = "";
        column_headers.clear();
        rows.clear();
        row_num = 0;
        tm = 0;
    }
};


struct oracle_stream
{
    bool is_oracle;
    std::string sessionId;
    std::string req_md5;

    std::string client_platform;
    std::string server_platform;
    std::string user_name;
    std::string client_tool;
    std::string dbname;

   	int preCallId;

    int protocol_version;
    std::string server_version;

    RequestInfo request;
    ResponseInfo response;

    void reset(){
    	request.reset();
    	response.reset();
    }

    bool hasRequest(){
    	return request.requeststr.length()>0;
    }

    bool hasHeader(){
        return response.column_headers.size()>0;
    }

    bool hasContent(){
        return response.rows.size()>0;
    }

  	bool to_server;
  	parser::oracle::SessionData session_data;
};

// }
// }

#endif //__PARSER_ORACLE_PARSER_ORACLE_STREAM_H__