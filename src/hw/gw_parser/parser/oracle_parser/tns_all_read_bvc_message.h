#ifndef __PARSER_ORACLE_PARSER_TNS_ALL_READ_BVC_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_ALL_READ_BVC_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class AllReadBvcMessage{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			stream.unmarshalUB2();
			int columnnum = stream.getResultSet().size();
			int size = columnnum/8;
			if (columnnum%8 != 0)
			{
				size += 1;
			}

			for (int i = 0; i < size; ++i)
			{
				int m = stream.unmarshalUB1();
				for (int j = 0; j < 8; ++j)
				{
					if ((m & (1<<j)) !=0)
					{
						bvcColSent += "1";
					}else{
						bvcColSent += "0";
					}
				}
			}
			stream.bvcValue = bvcColSent;
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	std::string bvcColSent= "";
};

}
}

#endif //__PARSER_ORACLE_PARSER_TNS_ALL_READ_BVC_MESSAGE_H__