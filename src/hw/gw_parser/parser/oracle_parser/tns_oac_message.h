#ifndef __PARSER_ORACLE_PARSER_TNS_OAC_MESSAGE__
#define __PARSER_ORACLE_PARSER_TNS_OAC_MESSAGE__

#include "session_data.h"

namespace parser{
namespace oracle{

class OacMessage{
public:
	OacMessage(){}

	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		// try{
			if (stream.platform==ClientPlatform::Linux)
			{
				flg = stream.unmarshalUB1();				
				dty = stream.unmarshalUB1();
			}else{
				dty = stream.unmarshalUB1();
				flg = stream.unmarshalUB1();
			}
			pre = stream.unmarshalUB1();

			if (stream.platform==ClientPlatform::Jdbc)
			{
				if(dty == 2 || dty == 180 || dty == 181 || dty == 231 || dty ==183){
					scl = stream.unmarshalUB2();
				}else{
					scl = stream.unmarshalUB1();
				}
			}else if(stream.platform==ClientPlatform::Linux){
				scl = stream.unmarshalUB2();
			}else if(stream.platform>=ClientPlatform::Win){
				scl = stream.unmarshalUB1();
			}
			xml = stream.unmarshalUB4();
			mal = stream.unmarshalSB4();
			fl2 = stream.unmarshalSB4();
			stream.unmarshalDALC(toid);
			vsn = stream.unmarshalUB2();
			csi = stream.unmarshalUB2();
			sfrm = stream.unmarshalUB1();
			if (stream.getTTCVersion()>=2)
			{
				mxlc = stream.unmarshalUB4();
			}
		// }catch(OverFlow&){
		// 	stream.clearData();
		// 	return false;
		// }

		return true;
	}


	int dty = 0;
	int flg = 0;
	int pre = 0;
	int scl = 0;
	int xml = 0;
	int mal = 0;
	int fl2 = 0;
	std::string toid = "";
	int vsn = 0;
	int csi = 0;
	int sfrm = 0;
	int mxlc = 0;
};

}
}

#endif //__PARSER_ORACLE_PARSER_TNS_OAC_MESSAGE__