#ifndef __PARSER_ORACLE_PARSER_TNS_REDIRECT_H__
#define __PARSER_ORACLE_PARSER_TNS_REDIRECT_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class TnsRedirect{
public:
	bool parseFromStream(SessionData &stream){
		// if (stream.remainLen()<2)
		// {
		// 	return false;
		// }
		// int offset = stream.getOffset();
		try{
			int len = stream.getNetShort();
			stream.getByteBuffer(redirectStr,len);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	std::string redirectStr = "";
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_REDIRECT_H__