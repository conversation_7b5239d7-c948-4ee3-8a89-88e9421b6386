#ifndef __PARSER_ORACLE_PARSER_TNS_ACCEPT_H__
#define __PARSER_ORACLE_PARSER_TNS_ACCEPT_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class TnsAccept{
public:
	bool parseFromStream(SessionData &stream){
		// if (stream.remainLen()<16)
		// {
		// 	return false;
		// }
		// int offset = stream.getOffset();
		try{			
			version = stream.getNetShort();
			servOption = stream.getNetShort();
			sduSize = stream.getNetShort();
			tduSize = stream.getNetShort();
			hardware = stream.getNetShort();
			int len = stream.getNetShort();
			int offset = stream.getNetShort();
			flag0 = stream.unmarshalUB1();
			flag1 = stream.unmarshalUB1();
			if (len > 0)
			{
				stream.getByteBuffer(acceptData,len);
			}
			stream.property.protoVersions= {version};
			stream.setProtoVersion(version);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	int version = 0;
	int servOption = 0;
	int sduSize = 0;
	int tduSize = 0;
	int hardware = 0;
	
	int flag0 = 0;
	int flag1 = 0;
	std::string acceptData = "";
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_ACCEPT_H__