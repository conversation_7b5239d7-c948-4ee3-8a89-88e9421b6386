#ifndef __PARSER_ORACLE_PARSER_TNS_VERSION_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_VERSION_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class VersionRequest{
public:
	bool parseFromStream(SessionData &stream){
		seqNumber = stream.unmarshalUB1();
		stream.unmarshalPtr();
		stream.unmarshalSWORD();
		if (stream.platform == ClientPlatform::Linux)
		{
			stream.unmarshalUB4();
		}
		stream.unmarshalPtr();
		stream.unmarshalPtr();
		return true;
	}

	int seqNumber = 0;
};

class VersionResponse{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			int size = stream.unmarshalUB2();
			stream.unmarshalCHR(rdbmsVersion,size);
			retverNum = stream.unmarshalUB4();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}


	std::string rdbmsVersion = "";
	int retverNum = 0;
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_VERSION_MESSAGE_H__