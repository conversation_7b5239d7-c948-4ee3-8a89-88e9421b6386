#ifndef __PARSER_ORACLE_PARSER_TNS_COMMONCALL_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_COMMONCALL_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class CommonCallMessage{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
		stream.unmarshalUB1();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_COMMONCALL_MESSAGE_H__