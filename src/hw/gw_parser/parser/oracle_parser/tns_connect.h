#ifndef __PARSER_ORACLE_PARSER_TNS_CONNECT_H__
#define __PARSER_ORACLE_PARSER_TNS_CONNECT_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class TnsConnect{
public:
	bool parseFromStream(SessionData &stream){
		// if (stream.remainLen()<26)
		// {
		// 	return false;
		// }
		// int offset = stream.getOffset();
		try{
			version = stream.getNetShort();
			compatibleVer = stream.getNetShort();
			servOption = stream.getNetShort();
			sduSize = stream.getNetShort();
			tduSize = stream.getNetShort();
			protocolCh = stream.getNetShort();
			lineTurnaround = stream.getNetShort();
			hardware = stream.getNetShort();
			int dataLength = stream.getNetShort();
			int offset = stream.getNetShort();
			maxData = stream.getNetInt();
			flag0 = stream.unmarshalUB1();
			flag1 = stream.unmarshalUB1();
			stream.setOffset(offset);
			stream.getByteBuffer(connectStr,dataLength);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}

		return true;
	}

	int version = 0;				//当前版本
	int compatibleVer = 0;			//向下兼容版本
	int servOption = 0;			
	int sduSize = 0;				//会话数据单元大小
	int tduSize = 0;				//最大传输数据单元啊小
	int protocolCh = 0;
	int lineTurnaround = 0;
	int hardware = 0;
	
	int maxData = 0;
	int flag0 = 0;
	int flag1 = 0;
	std::string connectStr = "";
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_CONNECT_H__