#ifndef __PARSER_ORACLE_PARSER_TNS_COMMAND_H__
#define __PARSER_ORACLE_PARSER_TNS_COMMAND_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class TnsCommand{
public:
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{
			cmdId = stream.unmarshalUB1();
			switch(cmdId){
			case 0x03:
			case 0x11:
				callId = stream.unmarshalUB1();
				break;
			default:
				break;
			}
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	int cmdId = 0;
	int callId = 0;

};
}
}
#endif //__PARSER_ORACLE_PARSER_TNS_COMMAND_H__