#ifndef __PARSER_ORACLE_PARSER_TNS_DATA_H__
#define __PARSER_ORACLE_PARSER_TNS_DATA_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class TnsData{
public:
	bool parseFromStream(SessionData &stream){
		// if (stream.remainLen()<2)
		// {
		// 	return false;
		// }

		// int offset = stream.getOffset();
		try{
			dataFlag = stream.getNetShort();
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	int dataFlag = 0;
};
}
}

#endif //__PARSER_ORACLE_PARSER_TNS_DATA_H__