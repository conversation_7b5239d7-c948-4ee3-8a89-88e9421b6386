#ifndef __PARSER_ORACLE_PARSER_TNS_REFUSE_H__
#define __PARSER_ORACLE_PARSER_TNS_REFUSE_H__

#include "session_data.h"

namespace parser{
namespace oracle{

class TnsRefuse{
public:
	bool parseFromStream(SessionData &stream){
		// if (stream.remainLen()<4)
		// {
		// 	return false;
		// }
		// int offset = stream.getOffset();
		try{
			userRefuseReason = stream.unmarshalUB1();
			sysResuseReason = stream.unmarshalUB1();

			int len = stream.getNetShort();
			stream.getByteBuffer(refuseStr,len);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}

		return true;
	}

	int userRefuseReason = 0;
	int sysResuseReason = 0;
	std::string refuseStr = "";
};
}
}
#endif //__PARSER_ORACLE_PARSER_TNS_REFUSE_H__