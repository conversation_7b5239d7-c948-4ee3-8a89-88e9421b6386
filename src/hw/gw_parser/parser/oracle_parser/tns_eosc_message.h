#ifndef __PARSER_ORACLE_PARSER_TNS_EOSC_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_EOSC_MESSAGE_H__

#include "session_data.h"

namespace parser{
namespace oracle{


class EOCSReponese9{
public:
	bool parseFromStream(SessionData &stream){
		// if (!stream.hasData())
		// {
		// 	return false;
		// }

		// int offset = stream.getOffset();
		try{
		switch(stream.eocsv){
			case 1:{
				eocsv = stream.unmarshalUB4();
				if ((eocsv & 0x8)!=0)
				{
					stream.unmarshalSB8();
				}
				//不break，继续执行后续解析
			}
			case 2:{
				//无eocsv内容，直接解析后面内容
				if (stream.getTTCVersion() >= 3)
				{
					endtoendecidSequenceNumber = stream.unmarshalUB2();
				}
				break;
			}

		}
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}


	int eocsv = 0;
	int unused = 0;
	int endtoendecidSequenceNumber = 0;
};


class EOCSReponse4{
public:
	bool parseFromStream(SessionData &stream){
		try{
			parseE4(stream);
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
	}

	void parseE4(SessionData &stream){
		if (stream.eocsv)
		{
			eocsv = stream.unmarshalUB4();
			if ((eocsv & 0x8)!=0)
			{
				stream.unmarshalSB8();
			}
		}
		if (stream.getTTCVersion() >= 3)
		{
			endtoendecidSequenceNumber = stream.unmarshalUB2();
		}
		if (stream.platform == ClientPlatform::Linux)
		{
			stream.unmarshalUB1();
		}
		curRowNumber = stream.unmarshalUB4();
		retcode = stream.unmarshalUB2();
		arrayElemError = stream.unmarshalUB2();
		arrayElemErrno = stream.unmarshalUB2();
		curCursorId = stream.unmarshalUB2();
		if (curCursorId > 0)
		{
			BindInfo info = stream.getBindInfo(0);
			info.id = curCursorId;
			stream.setBindInfo(info);
		}

		errorPosition = stream.unmarshalSB2();
		sqlType = stream.unmarshalUB1();
		oerFatal = stream.unmarshalSB1();
		flags = stream.unmarshalSB1();
		userCursorOpt = stream.unmarshalSB1();
		upiParam = stream.unmarshalUB1();
		warningFlag = stream.unmarshalUB1();
		rba = stream.unmarshalUB4();
		partitionId = stream.unmarshalUB2();
		tableId = stream.unmarshalUB1();
		blockNumber = stream.unmarshalUB4();
		slotNumber = stream.unmarshalUB2();
		osError = stream.unmarshalSWORD();
		stmtNumber = stream.unmarshalUB1();
		callNumber = stream.unmarshalUB1();
		pad1 = stream.unmarshalUB2();
		successIters = stream.unmarshalUB4();
		if (stream.platform == ClientPlatform::Linux)
		{
			stream.unmarshalUB4();
		}		

		std::string tmp = "";
		stream.unmarshalDALC(tmp);
		int num = stream.unmarshalUB2();
		for (int i = 0; i < num; ++i)
		{
			stream.unmarshalUB2();
		}
		num = stream.unmarshalUB4();
		for (int i = 0; i < num; ++i)
		{
			stream.unmarshalUB4();
		}
		if (stream.platform >= ClientPlatform::Win)
		{
			stream.unmarshalUB1();
		}else{
			stream.unmarshalUB2();
		}

		if (stream.platform == ClientPlatform::Linux)
		{
			for (int i = 0; i < 16; ++i)
			{				
				stream.unmarshalUB4();
			}
		}
		
		if (retcode > 0)
		{
			stream.unmarshalCLRforREFS(errorMsg);
		}
	}

	bool checkResult(SessionData &stream){
		if (retcode>0)
		{
			if (errorMsg.find(std::to_string(retcode))!=std::string::npos)
			{
				return true;
			}else{
				return false;
			}
		}else if(errorMsg.length() ==0){
			return true;
		}else{
			false;
		}
	}


	static void CheckMessage(SessionData &stream){
		if (stream.property.eocsvs.size() <2)
		{
			return;
		}

		int32_t curOffset = stream.getOffset();
		std::vector<int> tmpv;
		for(int evalue :stream.property.eocsvs){
			try{
				stream.setOffset(curOffset);
				EOCSReponse4 e4;
				e4.parseE4(stream);
				if (e4.checkResult(stream))
				{
					tmpv.push_back(evalue);
				}
			}catch(OverFlow&){}
		}

		if (tmpv.size()==1)
		{
			stream.eocsv = tmpv[0];
			stream.property.eocsvs = tmpv;
		}

	}

	int eocsv = 0;
	int unused = 0;
	int endtoendecidSequenceNumber = 0;
	int curRowNumber = 0;
	int retcode = 0;
	int arrayElemError = 0;
	int arrayElemErrno = 0;
	int curCursorId = 0;
	int errorPosition = 0;
	int sqlType = 0;
	int oerFatal = 0;
	int flags = 0;
	int userCursorOpt = 0;
	int upiParam = 0;
	int warningFlag = 0;
	int rba = 0;
	int partitionId = 0;
	int tableId = 0;
	int blockNumber = 0;
	int slotNumber = 0;
	int osError = 0;
	int stmtNumber = 0;
	int callNumber = 0;
	int pad1 = 0;
	int successIters = 0;

	std::string errorMsg = "";
};
}
}
#endif //__PARSER_ORACLE_PARSER_TNS_EOSC_MESSAGE_H__