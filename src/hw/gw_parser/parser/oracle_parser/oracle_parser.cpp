#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>
#include <arpa/inet.h>

#include "oracle_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"


#include "gw_stats.h"


#include "oracle_stream.h"

#include "cJSON.h"

namespace parser{
namespace oracle{

COracleParser::COracleParser(){
  m_quit_signal = 0;
  m_comm = NULL;
  memset(m_name, 0, sizeof(m_name));
  snprintf(m_name, COUNTOF(m_name) - 1, "COracleParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);
  memset(&m_stats_oracle, 0, sizeof(m_stats_oracle));

  m_pUpload = NULL;
  m_conf_upload_name = "";
  m_event_prior = 0;
  m_content_prior = 0;
}

COracleParser::~COracleParser(void)
{
}


 void COracleParser::cache_clean() 
 {

 }


/**
* 获取当前流解析出来的数据。
* @param struct StreamData *
* @param int dir
* @param int *data_len
* @param int *offset_out
*/
const char * COracleParser::get_data(const struct StreamData *, int dir, int *data_len, int *offset_out)
{
  *data_len = 0;
  *offset_out = 0;
  return NULL;
}

/**
* 已处理字节数。
* @param struct StreamData *
* @param int dir
* @param int num
*/
bool COracleParser::discard(struct StreamData *, int dir, int num)
{
  return false;
}

/**
* 已处理字节数，同时更新数据。
* @param struct StreamData *
* @param int dir
* @param int num
*/
bool COracleParser::discard_and_update(struct StreamData *, int dir, int num)
{
  return false;
}

// /**
//  * 删除解析对象中在会话管理中的单边数据。
//  * @param HalfStreamData*
//  */
// virtual void del_session_half_stream(HalfStreamData *);

/**
* @param StreamData*
*/
void COracleParser::del_session_stream(StreamData * psd){
	if(psd!=nullptr){
		delete psd->p_oracle_stream;
	}
}

/**
* @param SessionMgtData*
*/
void COracleParser::del_session_param(SessionMgtData *){

}

void COracleParser::init(){
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  load_conf(NULL);

  m_comm->get_gw_stats()->set_stats_callback("oracle show", print_oracle_stats_callback, this);
  m_comm->get_gw_stats()->set_qps("oracle session", &m_stats_oracle.cnt_session_total, 50);


  m_pUpload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
}

void COracleParser::fini(){
  ASSERT(m_comm != NULL);
}

void COracleParser::run(){
  ASSERT(m_comm != NULL);
}

/**
* 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
*/
const char * COracleParser::get_name(void) const
{
  return m_name;
}

/**
* 获取版本号。
*/
const char *COracleParser::get_version(void) const
{
  return  ORACLEPARSER_VER;
}

/**
* 设置全局公共类对象实例。
* @param CGwCommon *comm
*/
void COracleParser::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}


void COracleParser::parseServerList(std::string slist){
  std::string tmp = slist;
  do{
    std::string server = "";
    std::size_t idx = tmp.find(',');
    if (idx!=std::string::npos)
    {
      server = tmp.substr(0,idx);
      tmp = tmp.substr(idx+1);
    }else{
      server = tmp;
      tmp = "";
    }

    if (server.length() ==0)
    {
      continue;
    }

    std::string sip = "";
    std::string sport = "";
    idx = server.find(':');
    if (idx!=std::string::npos)
    {
      sip = server.substr(0,idx);
      sport = server.substr(idx+1);
    }else{
      sip = server;
    }

    if (sip.length() == 0)
    {
      continue;
    }

    uint32_t ip = inet_addr(sip.c_str());
    uint16_t port = 1521;
    if (sport.length()>0)
    {
      port = atoi(sport.c_str());
    }

    if (ip>0 && port >0)
    {
      struct addr s;
      s.ipv4= ip;
      s.port = port;
      server_list.push_back(s);
    }

  }while(tmp.length()>0);
}


/**
* 加载配置参数（Json字符串，支持动态）。
* @param const char *
*/
bool COracleParser::load_conf(const char *)
{
  CGwConfig *pgwc = m_comm->get_gw_config();


  
  m_conf_upload_name = pgwc->read_conf_string("parser", "upload_mode");

  std::string serverlist = pgwc->read_conf_string("parser","oracle_list");
  parseServerList(serverlist);


  // if (NULL != pgwc->get_section("brokers_list"))
  // {
  //   m_event_prior = pgwc->read_conf_int("hiveevent", "priority", 0);
  //   m_content_prior = pgwc->read_conf_int("hivecontent", "priority", 0);
  // }
  // else
  // {
  //   // 兼容老配置
  //   m_event_prior = KAFKA_UPLOAD_PRIOR_HIVE_EVENT;
  //   m_content_prior = KAFKA_UPLOAD_PRIOR_HVIE_CONTENT;
  // }

  // if (json_string != NULL)
  // {
  //   // TODO 动态加载配置参数
  //   pgwc->load_string(json_string);
  // }

  return true;
}

/**
* 触发退出信号时处理
*/
void COracleParser::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
* 等待运行结束
*/
void COracleParser::wait_for_stop(void)
{
}

/**
* 设置过滤规则。
* @param CFilterRule*rule
*/
void COracleParser::set_url_filter_rule(CFilterRule *rule){}

/**
 *  设置账号过滤规则 
 *  @param CFilterRule *rule
 */
void COracleParser::set_accout_filter_rule(CFilterRule *rule){}

void COracleParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) {}

// void CHBaseParser::free_worker_queue(CWorkerQueue *p)
// {
//   if (p == NULL)
//   {
//     return;
//   }
//   p->set_quit_signal();
//   p->wait_for_stop();

//   p->delete_queue();

//   p->fini();

//   if (m_comm != NULL)
//   {
//     m_comm->destory_worker_queue(p);
//   }
// }

// void CHBaseParser::free_task_worker(CTaskWorker *p)
// {
//   if (p == NULL)
//   {
//     return;
//   }
//   p->release();
// }

/**
* 增加上层协议解析对象。
* @param CParser *parser
*/
void COracleParser::add_upstream(CParser *parser){}

/**
* 清空上层协议解析对象
*/
void COracleParser::reset_upstream(void){}

/**
* 推送到上层消息(异步方式, Json序列化数据)
* @param char *s
* @param size_t *length
*/
void COracleParser::push_upstream_msg(char *s, size_t length)
{
  GWLOG_TEST(m_comm, "hbase test s=%p length=%u\n", s, length);
}

/**
* 是否使用当前协议解析流数据
* @param struct StreamData*
*/
bool COracleParser::is_parsed(const struct StreamData *) const
{
  return false;
}

/**
* 克隆会话流数据到队列中使用(预留)
* @param struct StreamData*
*/
struct StreamData *COracleParser::clone_stream_data(const struct StreamData *)
{
  return NULL;
}

/**
 *  获取解析http数量(针对http parser) 
 */
uint64_t COracleParser::get_parser_http_cnt()
{
  return 0;
}

/**
 *  获取解析http成功的数量(针对http parser) 
 */
uint64_t COracleParser::get_succ_parser_http_cnt()
{
  return 0;
}

/**
 *  获取解析parser的状态数据，以便于进行查看Parser内部状态
 */
void* COracleParser::get_parser_status()
{
  return NULL;
}

void COracleParser::print_oracle_stats_callback(void *p)
{
    COracleParser *pThis = (COracleParser *)p;
    ASSERT(pThis != NULL);
    pThis->print_oracle_stats();
}

void COracleParser::print_oracle_stats(void)
{
    printf ("\n%-20s %12s %12s %12s\n", "stats oracle ", "total", "success", "failure");
    printf ("oracle session:       %12" PRIu64 " %12" PRIu64 "\n", m_stats_oracle.cnt_session_total, m_stats_oracle.cnt_session_closed);
    printf ("oracle exec sql:      %12" PRIu64 "\n", m_stats_oracle.cnt_sql);
    printf ("oracle parser:        %12" PRIu64 " %12" PRIu64 "\n", m_stats_oracle.cnt_parser_request, m_stats_oracle.cnt_parser_response);
}

}
}