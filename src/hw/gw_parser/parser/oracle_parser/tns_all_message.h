#ifndef __PARSER_ORACLE_PARSER_TNS_ALL_MESSAGE_H__
#define __PARSER_ORACLE_PARSER_TNS_ALL_MESSAGE_H__

#include <string.h>

#include "session_data.h"
#include "tns_oac_message.h"
#include "tns_rxd_message.h"

namespace parser{
namespace oracle{

class AllMessage{
public:
	AllMessage(int id):callId(id){}
	bool parseFromStream(SessionData &stream){
		// int offset = stream.getOffset();
		try{		
			seqNumber = stream.unmarshalUB1();

			if (callId == 5)
			{
				cursor = stream.unmarshalSWORD();
				options = stream.unmarshalSWORD();
			}else if (callId == 0x4e)
			{
				cursor = stream.unmarshalSWORD();
				options = stream.unmarshalSWORD();
				stream.unmarshalUB4();
				stream.unmarshalUB4();
			}else if(callId == 0x5e){	
				if (stream.property.codeNumber.size()>1)
				{
					checkType(stream);
				}
				options = stream.unmarshalUB4();
				cursor = stream.unmarshalSWORD();
				stream.unmarshalPtr();
				sqlLen = stream.unmarshalSWORD();

				unsigned char tmp3[] = {0xff,0xff,0xff,0xff,0xff,0xff};
				if ((stream.property.platforms.size()>1) && ((stream.getTypeRep(1)&0x01) == 0) && (!stream.check(1,tmp3,6)))
				{
					stream.property.platforms = {1};
					stream.property.protoVersions = {314};
					stream.SetProperty();
				}
				
				if ((stream.platform == ClientPlatform::Linux) && (stream.getProtoVersion() >= 314))
				{
					stream.unmarshalUB4();
				}
				stream.unmarshalPtr();
				int opsize = stream.unmarshalSWORD();
				if ((stream.platform == ClientPlatform::Linux) && (stream.getProtoVersion() >= 314))
				{
					stream.unmarshalUB4();
				}
				stream.unmarshalPtr();
				stream.unmarshalPtr();

				stream.unmarshalUB4();
				rowsToFetch = stream.unmarshalUB4();

				long pcvalue = stream.unmarshalUB4();
				if (pcvalue == 2147483647L)
				{
					plsqlOrCall = false;
				}else if (pcvalue == 32760L)
				{
					plsqlOrCall = true;
				}

				if ((stream.platform == ClientPlatform::Linux) && (stream.getProtoVersion() >= 314))
				{
					stream.unmarshalUB4();
					stream.unmarshalPtr();
					numberOfBindPositions = stream.unmarshalSWORD();
					stream.unmarshalUB4();
				}else{
					stream.unmarshalPtr();
					numberOfBindPositions = stream.unmarshalSWORD();
					stream.unmarshalPtr();
				}


				stream.unmarshalPtr();
				stream.unmarshalPtr();
				stream.unmarshalPtr();
				stream.unmarshalPtr();
				stream.unmarshalPtr();

				if ((stream.platform == ClientPlatform::Linux) && (stream.getProtoVersion() >= 314))
				{
					stream.unmarshalPtr();
				}

				defCols = stream.unmarshalSWORD();

				if (stream.property.ttcVersions.size()>2)
				{
					checkSql(stream);
				}else{
					try{
						parseSql(stream,sql);
					}catch(OverFlow&){}
				}
				if (sqlLen > 0 && sql.length() == 0)
				{
					searchSql(stream);
					stream.clearData();
					return true;
				}	
					

				BindInfo info;
				if (sqlLen>0)
				{				
					info.id = 0;
					info.sql = sql;
				}else{
					info = stream.getBindInfo(cursor);
					sql = info.sql;
				}
					
				for (int i = 0; i < opsize; ++i)
				{
					opArray.push_back(stream.unmarshalUB4());
				}


				if ((options & 0x8) != 0 && numberOfBindPositions >0)
				{
					for (int i = 0; i < numberOfBindPositions; ++i)
					{
						OacMessage oac;
						oac.parseFromStream(stream);
						info.bindtypes.push_back(oac.dty);
					}
					stream.setBindInfo(info);
					if (stream.platform == ClientPlatform::Linux)
					{
						stream.unmarshalUB1();
						stream.unmarshalUB4();
						stream.unmarshalUB4();
						stream.unmarshalUB2();
					}
				}


				if (stream.getTTCVersion() >= 2 && ((options& 0x10) != 0))
				{
					for (int i = 0; i < defCols; ++i)
					{
						OacMessage oac;
						oac.parseFromStream(stream);
					}
					if (stream.platform == ClientPlatform::Linux)
					{
						stream.unmarshalUB1();
						stream.unmarshalUB4();
						stream.unmarshalUB4();
						stream.unmarshalUB2();
					}
				}

				if ((options & 0x20) != 0)
				{
					if(stream.hasData()){
						if (cursor>0)
						{
							RxdMessage rxd(stream.getBindInfo(cursor));
							rxd.parseFromStream(stream);
							this->values = rxd.values;
						}else{
							RxdMessage rxd(info);
							rxd.parseFromStream(stream);
							this->values = rxd.values;
						}
					}
				}

				stream.clearData();
			}
		}catch(OverFlow&){
			stream.clearData();
			return false;
		}
		return true;
	}

	void checkType(SessionData &stream){

		unsigned char tmp[] = {0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xfe};
		unsigned char tmp2[] = {0xfe,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
		if (stream.check(8,tmp,8))  //不压缩
		{						
			stream.property.codeNumber = {0};
			stream.property.codePtr = {0};
		}else if (stream.check(8,tmp2,8)){		//不压缩						
			stream.property.codeNumber = {2};
			stream.property.codePtr = {2};
		}else{												
			stream.property.codeNumber = {1};

			uint32_t offset = stream.getOffset();

			stream.unmarshalUB4();
			stream.unmarshalUB4();

			if (stream.check(0,tmp,8))
			{
				stream.property.codePtr = {0};
			}else if(stream.check(0,tmp2,8)){
				stream.property.codePtr = {2};
			}else{
				stream.property.codePtr = {1};
			}

		}
		stream.SetProperty();

	}

	void parseSql(SessionData &stream,std::string& tmpsql){
		if (stream.getTTCVersion() >=4)
		{
			regId = stream.unmarshalUB4();
			stream.unmarshalPtr();
			if (stream.getProtoVersion() <314 || (stream.platform != ClientPlatform::Linux))
			{					
				stream.unmarshalPtr();
			}
			long long tmp = 0;

			if (stream.getTTCVersion()>=5)
			{
				stream.unmarshalPtr();
				stream.unmarshalUB4();
				stream.unmarshalPtr();
				stream.unmarshalUB4();
				tmp = stream.unmarshalUB4();
				regId = (tmp<<32)| regId;
			}
		}

		if ((stream.platform == ClientPlatform::Linux) && (stream.getProtoVersion() >= 314))
		{	
			stream.unmarshalUB4();
			stream.unmarshalUB4();
			stream.unmarshalUB4();
		}

		if (sqlLen>0)
		{
			stream.unmarshalCHR(tmpsql,sqlLen);
			// printf("sql:%s\n", sql.c_str());
		}
	}

	void checkSql(SessionData &stream){
		uint32_t curOffset = stream.getOffset();
		int ttc = 0;
		std::string tmpsql = "";
		for (int i = 3; i < 6; ++i)
		{
			stream.setTTCVersion(i);
			stream.setOffset(curOffset);
			try{		
				tmpsql = "";
				parseSql(stream,tmpsql);
				bool check = true;
				for(uint8_t c :tmpsql){
					if (c<0x20 || c>0x7e)
					{
						check = false;
						break;
					}
				}

				if (check && (tmpsql.length()>sql.length()))
				{
					ttc = i;
					sql = tmpsql;
				}

			}catch(OverFlow&){}
		}

		switch(ttc){
		case 3:
			stream.property.ttcVersions = {2,3};
		break;
		case 4:
			stream.property.ttcVersions = {4};
		break;
		case 5:
			stream.property.ttcVersions = {5,6};
		break;
		case 0:
			stream.setOffset(curOffset);
			return;
		}

		stream.setTTCVersion(stream.property.ttcVersions[0]);
		return;
	}

	void searchSql(SessionData &stream){
		int i = 3;
		stream.setOffset(20);
		for (; i < stream.remainLen(); ++i)
		{
			//连续三个字符是可视字符，确认是sql内容
			if(stream.isLetter(i-2)
				&& stream.isLetter(i-1)
				&& stream.isLetter(i)){
				break;
			}
		}
		//找到了sql内容起始位置
		if (i<stream.remainLen())
		{
			unsigned char sec = 0;
			int sqlidx = 0;
			if (stream.getChar(i-4) == 0xfe &&(stream.getChar(i-3) == 0xff))
			{
				sec = stream.getChar(i-3);
				sqlidx = i-2;

			}else if (stream.getChar(i-3) == 0xfe &&( stream.getChar(i-2) == 0x40))
			{
				sec = stream.getChar(i-2);
				sqlidx = i-1;
			}else{
				sqlidx = i-2;
			}

			if (sec>0)
			{
				int nexidx = sqlidx+sec;
				for (; sqlidx < stream.remainLen(); ++sqlidx)
				{
					if (sqlidx == nexidx && stream.getChar(sqlidx) == sec)
					{
						nexidx += 1;
						nexidx+=sec;
						continue;
					}

					if (!stream.isVisibleChar(sqlidx))
					{
						break;
					}

					sql.append(1,stream.getChar(sqlidx));
				}
			}else{
				for (; sqlidx < stream.remainLen(); ++sqlidx)
				{
					if (!stream.isVisibleChar(sqlidx))
					{
						break;
					}

					sql.append(1,stream.getChar(sqlidx));
				}
			}
		}
		stream.clearData();
	}

	int callId = 0;
	int seqNumber = 0;
	int options = 0;
	std::vector<int> opArray;
	int cursor = 0;
	int rowsToFetch = 0;
	bool plsqlOrCall = false;
	int numberOfBindPositions = 0;
	int defCols = 0;

	// std::vector<OacMessage> binds;
	// std::vector<OacMessage> defDefines;
	// std::vector<RdxMessage> rdxs;

	long long regId = 0;
	int sqlLen = 0;
	std::string sql = "";
	std::vector<std::string> values;

};
}
}


#endif  //__PARSER_ORACLE_PARSER_TNS_ALL_MESSAGE_H__