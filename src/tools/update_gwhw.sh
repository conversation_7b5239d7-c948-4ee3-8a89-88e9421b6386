#!/bin/bash

# 简化的清理函数 - 批量删除所有匹配的定时任务
cleanup_all_upgrade_cron() {
    # 删除所有升级相关的定时任务
    crontab -l 2>/dev/null | grep -v "sh /opt/apigw/gwhw/tools/update_gwhw.sh" | crontab -
}

# 脚本开始就先清理一次（清理可能残留的定时任务）
cleanup_all_upgrade_cron

# 设置 trap 确保异常退出时也能清理
trap 'cleanup_all_upgrade_cron; rm -rf /home/<USER>/' EXIT

if [ -f /home/<USER>/1.txt ]; then
    rm -f /home/<USER>/1.txt
    # echo "need update gwhw" >> /home/<USER>
else
    cleanup_all_upgrade_cron
    # echo "cancel update gwhw" >> /home/<USER>
    exit 1
fi

gw_parser_config_file_path="/opt/data/apigw/gwhw/gw_parser.conf"

source_path_content=$(grep "source_path" $gw_parser_config_file_path | tail -1)
# echo "source_path_content=$source_path_content" >> /home/<USER>
load_files_content=$(grep "load_files" $gw_parser_config_file_path)
# echo "load_files_content=$load_files_content" >> /home/<USER>
nic_device_name_content=$(grep "nic_device_name" $gw_parser_config_file_path)
# echo "nic_device_name_content=$nic_device_name_content" >> /home/<USER>

cd /home/<USER>/
# 检查是否已经解压（通过判断 inst 目录是否存在）
if [ ! -d "inst" ]; then
    # 如果 inst 目录不存在，说明未解压，需要解压
    unzip -q update_gwhw.zip
fi
cd inst/
bash inst.sh -t Compatible -p API -v 3.1

# echo "install gwhw finish" >> /home/<USER>

# echo "source_path_content=$source_path_content" >> /home/<USER>
lineNum=$(grep -n "source_path" $gw_parser_config_file_path | tail -1 | cut -d : -f 1)
# echo "source_path line num=$lineNum" >> /home/<USER>
sed -i "${lineNum}c \\${source_path_content}" $gw_parser_config_file_path

# echo "load_files_content=$load_files_content" >> /home/<USER>
lineNum=$(grep -n "load_files" $gw_parser_config_file_path | cut -d : -f 1)
# echo "load_files line num=$lineNum" >> /home/<USER>
sed -i "${lineNum}c \\${load_files_content}" $gw_parser_config_file_path

# echo "nic_device_name_content=$nic_device_name_content" >> /home/<USER>
lineNum=$(grep -n "nic_device_name" $gw_parser_config_file_path | cut -d : -f 1)
# echo "nic_device_name line num=$lineNum" >> /home/<USER>
sed -i "${lineNum}c \\${nic_device_name_content}" $gw_parser_config_file_path

systemctl restart gwhw

# 清理定时任务和升级资源
cleanup_all_upgrade_cron
rm -rf /home/<USER>/