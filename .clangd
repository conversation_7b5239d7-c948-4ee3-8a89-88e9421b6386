CompileFlags:
  Add:
    # C++标准
    - -std=c++11
    
    # 编译器定义
    - -D_CC_GNU_PP
    - -DNDEBUG
    - -D_ENABLE_FLUSH_FILE=1
    - -DKiB=1024
    - -DMiB=1048576
    
    # 基础包含路径
    - -I.
    - -I./src/hw/gw_parser/include
    - -I./src/hw/gw_parser/utils/queue
    - -I./src/hw/gw_parser/utils/cjson
    - -I./src/hw/gw_parser/core/l4
    - -I./src/hw/gw_parser/core
    - -I./src/hw/gw_parser/utils
    
    # 解析器相关路径
    - -I./src/hw/gw_parser/parser/oracle_parser
    - -I./src/hw/gw_parser/parser/mysql_parser
    - -I./src/hw/gw_parser/parser/postgre_parser
    - -I./src/hw/gw_parser/parser/ssl_parser
    - -I./src/hw/gw_parser/parser/http_parser
    - -I./src/hw/gw_parser/parser/http2_parser
    - -I./src/hw/gw_parser/parser/grpc_parser
    - -I./src/hw/gw_parser/parser/mongo_parser
    - -I./src/hw/gw_parser/parser/ftp_parser
    - -I./src/hw/gw_parser/parser
    
    # 数据源和上传模块路径
    - -I./src/hw/gw_parser/source
    - -I./src/hw/gw_parser/source/dpdk_source
    - -I./src/hw/gw_parser/source/nic_source
    - -I./src/hw/gw_parser/source/pcap_source
    - -I./src/hw/gw_parser/source/file_source
    - -I./src/hw/gw_parser/upload
    - -I./src/hw/gw_parser/upload/kafka_upload
    - -I./src/hw/gw_parser/upload/log_upload
    - -I./src/hw/gw_parser/upload/diy_upload
    - -I./src/hw/gw_parser/upload/web_upload
    
    # 第三方库包含路径
    - -I./src/hw/gw_parser/nacos_c++/include
    - -I./src/hw/gw_parser/libaws_api_c++/include
    - -I./src/hw/gw_parser/libmagic/include
    - -I./src/hw/gw_parser/liblicutils_c_sdk
    
    # gw_stats_srv_oatpp 项目相关路径
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/include
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/log
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/rapidjson
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/controller
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/oatpp-1.3.0/oatpp
    - -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/test/app
    
    # 系统包含路径
    - -I/usr/include
    - -I/usr/local/include
    - -I/usr/libiconv/include
    - -I/opt/openssl/include
    
    # 编译器标志
    - -fPIC
    - -Wall
    - -g
    
    # 针对macOS的特殊配置
    - -D_DARWIN_C_SOURCE
    
  Remove:
    # 移除可能导致问题的标志
    - -fsanitize=address
    - -fno-omit-frame-pointer

# 诊断配置
Diagnostics:
  ClangTidy:
    Add: 
      - readability-*
      - performance-*
    Remove:
      # 屏蔽所有modernize规则，避免影响使用体验
      - modernize-*
      # 屏蔽特定的可读性规则
      - readability-magic-numbers
      - readability-identifier-length
      - readability-function-cognitive-complexity
      - readability-else-after-return
  UnusedIncludes: None
  MissingIncludes: None

# 索引配置
Index:
  Background: Build
  StandardLibrary: Yes

# 代码补全配置
Completion:
  AllScopes: Yes