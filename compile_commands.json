[{"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_parser_deal_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_parser_deal_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_parser_deal_probe.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_parser_deal_probe.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_tns_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_tns_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_ttc_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_ttc_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_tti_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_tti_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_ttc_stack_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_ttc_stack_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_auth_complete.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_auth_complete.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_memory_manager.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_memory_manager.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_data_descriptor.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_data_descriptor.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_sql_executor.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_sql_executor.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_transaction_manager.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_transaction_manager.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_error_handler.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_error_handler.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_data_types.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_data_types.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_version_compat.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_version_compat.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_lob_handler.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_lob_handler.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_batch_handler.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_batch_handler.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_procedure_handler.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_procedure_handler.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_auth_handler.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_auth_handler.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_session_manager.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_session_manager.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_advanced_types.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_advanced_types.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_complex_types.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_complex_types.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c module_mgt_oracle_parser.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/module_mgt_oracle_parser.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_parser_parser_msg.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_parser_parser_msg.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "g++ -g -fvisibility=hidden -fPIC -std=c++11 -D_CC_GNU_PP -DNDEBUG -D_ENABLE_FLUSH_FILE=1 -Wall -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c oracle_parser_upload_task_worker.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/oracle_parser_upload_task_worker.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "gcc -g -fvisibility=hidden -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c cJSON.c", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/../.././utils/cjson/cJSON.c"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "gcc -g -fvisibility=hidden -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c cJSON_Utils.c", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/../.././utils/cjson/cJSON_Utils.c"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser", "command": "gcc -g -fvisibility=hidden -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././core/l4/ -I../.././core/ -I../.././utils/ -I/usr/include -I/usr/libiconv/include -c utils.c", "file": "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/oracle_parser/../../core/utils.c"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/App.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/App.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/controller/GwStatService.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/controller/GwStatService.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/AppStats.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/AppStats.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/AppLicense.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/AppLicense.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/AppSettings.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/AppSettings.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/AppEth.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/AppEth.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/AppHealth.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/AppHealth.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/AppTask.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/AppTask.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/app/Authorization.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/Authorization.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I. -I./oatpp-1.3.0/oatpp/oatpp -I./src/rapidjson -I./src/app/include -I./src/log -I./src/dto -I./src/controller -I/opt/openssl/include -c src/log/Log.cpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/log/Log.cpp"}, {"directory": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp", "command": "g++ -g -fPIC -std=c++11 -D KiB=1024 -D MiB=1048576 -Wall -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/oatpp-1.3.0/oatpp/oatpp -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/rapidjson -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/app/include -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/log -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/dto -I/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/controller -I/opt/openssl/include -fsyntax-only src/controller/GwStatService.hpp", "file": "/Users/<USER>/gw-hw/src/hw/gw_stats_srv_oatpp/src/controller/GwStatService.hpp"}]