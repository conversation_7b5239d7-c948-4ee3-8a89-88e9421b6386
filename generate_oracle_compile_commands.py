#!/usr/bin/env python3
"""
生成Oracle解析器的compile_commands.json的Python脚本
基于Makefile中的配置生成精确的编译命令
"""

import json
import os
import glob

def generate_compile_commands():
    project_root = "/Users/<USER>/gw-hw"
    oracle_parser_dir = os.path.join(project_root, "src/hw/gw_parser/parser/oracle_parser")
    
    # 基础编译配置（从Makefile提取）
    base_cflags = [
        "-g", "-fvisibility=hidden", "-fPIC", "-std=c++11",
        "-D_CC_GNU_PP", "-DNDEBUG", "-D_ENABLE_FLUSH_FILE=1", "-Wall"
    ]
    
    # 包含路径（从Makefile提取）
    include_paths = [
        "-I.",
        "-I../.././include",
        "-I../.././utils/queue/",
        "-I../.././utils/cjson/",
        "-I../.././core/l4/",
        "-I../.././core/",
        "-I../.././utils/",
        "-I/usr/include",
        "-I/usr/libiconv/include"
    ]
    
    commands = []
    
    # Oracle解析器的源文件列表（从Makefile提取）
    oracle_sources = [
        "oracle_parser.cpp",
        "oracle_parser_deal_parser.cpp", 
        "oracle_parser_deal_probe.cpp",
        "oracle_tns_parser.cpp",
        "oracle_ttc_parser.cpp",
        "oracle_tti_parser.cpp",
        "oracle_ttc_stack_parser.cpp",
        "oracle_auth_complete.cpp",
        "oracle_memory_manager.cpp",
        "oracle_data_descriptor.cpp",
        "oracle_sql_executor.cpp",
        "oracle_transaction_manager.cpp",
        "oracle_error_handler.cpp",
        "oracle_data_types.cpp",
        "oracle_version_compat.cpp",
        "oracle_lob_handler.cpp",
        "oracle_batch_handler.cpp",
        "oracle_procedure_handler.cpp",
        "oracle_auth_handler.cpp",
        "oracle_session_manager.cpp",
        "oracle_advanced_types.cpp",
        "oracle_complex_types.cpp",
        "module_mgt_oracle_parser.cpp",
        "oracle_parser_parser_msg.cpp",
        "oracle_parser_upload_task_worker.cpp"
    ]
    
    # 为每个源文件生成编译命令
    for source_file in oracle_sources:
        file_path = os.path.join(oracle_parser_dir, source_file)
        if os.path.exists(file_path):
            command_parts = ["g++"] + base_cflags + include_paths + ["-c", source_file]
            command = {
                "directory": oracle_parser_dir,
                "command": " ".join(command_parts),
                "file": file_path
            }
            commands.append(command)
    
    # 添加公共文件
    common_files = [
        ("../.././utils/cjson/cJSON.c", "gcc"),
        ("../.././utils/cjson/cJSON_Utils.c", "gcc"),
        ("../../core/utils.c", "gcc")
    ]
    
    for common_file, compiler in common_files:
        full_path = os.path.join(oracle_parser_dir, common_file)
        if os.path.exists(full_path):
            if compiler == "gcc":
                cflags_c = ["-g", "-fvisibility=hidden", "-fPIC"]
                command_parts = [compiler] + cflags_c + include_paths + ["-c", os.path.basename(common_file)]
            else:
                command_parts = [compiler] + base_cflags + include_paths + ["-c", os.path.basename(common_file)]
            
            command = {
                "directory": oracle_parser_dir,
                "command": " ".join(command_parts),
                "file": full_path
            }
            commands.append(command)
    
    # 写入compile_commands.json
    output_file = os.path.join(project_root, "compile_commands.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(commands, f, indent=2, ensure_ascii=False)
    
    print(f"成功生成compile_commands.json，包含{len(commands)}个编译命令")
    print(f"输出文件: {output_file}")
    
    return output_file

if __name__ == "__main__":
    generate_compile_commands()